/**
 * Database model interfaces
 */

export interface Subject {
  id: string;
  title: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface SubjectWithCurriculum extends Subject {
  has_curriculum: boolean;
}

export interface Curriculum {
  id: string;
  subject_id: string;
  structure: string; // JSON string
  created_at: string;
  updated_at: string;
}

export interface CurriculumWithSubject extends Curriculum {
  subject_title: string;
  subject_description: string | null;
}

export interface Course {
  id: string;
  subject_id: string;
  topic_id: string;
  topic_title: string;
  content: string;
  created_at: string;
}

export interface UserProgress {
  id: string;
  subject_id: string;
  topic_id: string;
  completed: boolean;
  last_accessed: string;
}

/**
 * Curriculum structure types
 */
export interface Topic {
  id: string;
  title: string;
  description?: string;
}

export interface Category {
  id: string;
  title: string;
  description?: string;
  topics: Topic[];
}

export interface CurriculumStructure {
  curriculum: Category[];
}

/**
 * Database operation result types
 */
export interface DatabaseResult {
  id?: number;
  changes: number;
}

/**
 * Database helper function types
 */
export type DatabaseRow = Record<string, any>;
export type DatabaseParams = (string | number | boolean | null)[];
