import { Router } from 'express';
import { GoogleGenAI } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import {
  GenerateCurriculumRequest,
  GenerateCurriculumResponse,
  CreateCurriculumRequest,
  CreateCurriculumResponse,
  CurriculaListResponse,
  CurriculumResponse,
  RouteHandler,
  CurriculumStructure
} from '../types/index.js';
import { DatabaseHelper } from '../utils/database.js';
import { 
  validateGenerateCurriculumRequest, 
  validateCreateCurriculumRequest,
  validateIdParam 
} from '../utils/validation.js';
import { sendErrorResponse, NotFoundError, LLMError } from '../utils/errors.js';

export function createCurriculumRoutes(dbHelper: DatabaseHelper, ai: GoogleGenAI): Router {
  const router = Router();

  // Generate curriculum
  const generateCurriculum: RouteHandler<GenerateCurriculumRequest, GenerateCurriculumResponse> = async (req, res) => {
    try {
      const { subject } = validateGenerateCurriculumRequest(req.body);

      const curriculumPrompt = `**Your Role:** You are an expert curriculum designer and educational content creator.

**Your Task:** Generate a comprehensive, well-structured learning curriculum for the subject: **"${subject}"**.

**Requirements:**
- Create a curriculum with 4-8 main categories/modules
- Each category should have 3-8 specific topics/lessons
- Topics should progress from beginner to advanced concepts
- Focus on practical, hands-on learning
- Ensure logical learning progression

**Output Format:**
You **MUST** respond with a valid JSON object in this exact structure:
{
  "curriculum": [
    {
      "id": "category-1",
      "title": "Category Title",
      "description": "Brief description of what this category covers",
      "topics": [
        {
          "id": "topic-1",
          "title": "Topic Title",
          "description": "Brief description of the topic"
        }
      ]
    }
  ]
}

**Important:**
- Use kebab-case for all IDs (lowercase with hyphens)
- Keep titles concise but descriptive
- Ensure the curriculum is comprehensive but not overwhelming
- Focus on practical skills and real-world applications`;

      const llmResponse = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: curriculumPrompt,
        config: {
          thinkingConfig: {
            thinkingBudget: 0,
          },
        },
      });

      // Extract and parse the curriculum content
      let curriculumText = (llmResponse.text || '').trim();

      // Remove markdown code blocks if present
      if (curriculumText.startsWith('```json')) {
        curriculumText = curriculumText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (curriculumText.startsWith('```')) {
        curriculumText = curriculumText.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      let curriculum: CurriculumStructure;
      try {
        curriculum = JSON.parse(curriculumText);
      } catch (parseError) {
        console.error("Failed to parse curriculum JSON:", parseError);
        throw new LLMError("Failed to generate valid curriculum structure. Please try again.");
      }

      res.json(curriculum);
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Get all curricula
  const getAllCurricula: RouteHandler<void, CurriculaListResponse> = async (_req, res) => {
    try {
      const curricula = await dbHelper.all(`
        SELECT c.*, s.title as subject_title, s.description as subject_description
        FROM curricula c
        JOIN subjects s ON c.subject_id = s.id
        ORDER BY c.updated_at DESC
      `);

      const formattedCurricula = curricula.map(curriculum => ({
        id: curriculum['id'] as string,
        subject_id: curriculum['subject_id'] as string,
        subject_title: curriculum['subject_title'] as string,
        subject_description: curriculum['subject_description'] as string | null,
        structure: curriculum['structure'] as string,
        created_at: curriculum['created_at'] as string,
        updated_at: curriculum['updated_at'] as string
      }));

      res.json(formattedCurricula);
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Get curriculum by subject ID
  const getCurriculumBySubjectId: RouteHandler<void, CurriculumResponse> = async (req, res) => {
    try {
      const subjectId = validateIdParam(req.params['subjectId'] as string);

      const curriculum = await dbHelper.get(`
        SELECT c.*, s.title as subject_title, s.description as subject_description
        FROM curricula c
        JOIN subjects s ON c.subject_id = s.id
        WHERE c.subject_id = ?
      `, [subjectId]);

      if (!curriculum) {
        throw new NotFoundError("Curriculum not found");
      }

      const formattedCurriculum: CurriculumResponse = {
        id: curriculum['id'] as string,
        subject_id: curriculum['subject_id'] as string,
        subject_title: curriculum['subject_title'] as string,
        subject_description: curriculum['subject_description'] as string | null,
        structure: JSON.parse(curriculum['structure'] as string),
        created_at: curriculum['created_at'] as string,
        updated_at: curriculum['updated_at'] as string
      };

      res.json(formattedCurriculum);
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Create or update curriculum
  const createOrUpdateCurriculum: RouteHandler<CreateCurriculumRequest, CreateCurriculumResponse> = async (req, res) => {
    try {
      const { subjectId, structure } = validateCreateCurriculumRequest(req.body);

      // Check if curriculum already exists for this subject
      const existingCurriculum = await dbHelper.get(
        "SELECT id FROM curricula WHERE subject_id = ?",
        [subjectId]
      );

      if (existingCurriculum) {
        // Update existing curriculum
        await dbHelper.run(`
          UPDATE curricula
          SET structure = ?, updated_at = CURRENT_TIMESTAMP
          WHERE subject_id = ?
        `, [JSON.stringify(structure), subjectId]);

        res.json({
          id: existingCurriculum['id'] as string,
          subjectId,
          message: "Curriculum updated successfully"
        });
      } else {
        // Create new curriculum
        const curriculumId = uuidv4();
        await dbHelper.run(`
          INSERT INTO curricula (id, subject_id, structure)
          VALUES (?, ?, ?)
        `, [curriculumId, subjectId, JSON.stringify(structure)]);

        res.json({
          id: curriculumId,
          subjectId,
          message: "Curriculum created successfully"
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Delete curriculum
  const deleteCurriculum: RouteHandler<void, { message: string }> = async (req, res) => {
    try {
      const subjectId = validateIdParam(req.params['subjectId'] as string);

      const result = await dbHelper.run("DELETE FROM curricula WHERE subject_id = ?", [subjectId]);

      if (result.changes === 0) {
        throw new NotFoundError("Curriculum not found");
      }

      res.json({ message: "Curriculum deleted successfully" });
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Register routes
  router.post('/generate-curriculum', generateCurriculum);
  router.get('/curricula', getAllCurricula);
  router.get('/curricula/:subjectId', getCurriculumBySubjectId);
  router.post('/curricula', createOrUpdateCurriculum);
  router.delete('/curricula/:subjectId', deleteCurriculum);

  return router;
}
