import { Router } from 'express';
import { GoogleGenAI } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import {
  GenerateCourseRequest,
  GenerateCourseResponse,
  RouteHandler
} from '../types/index.js';
import { DatabaseHelper } from '../utils/database.js';
import { validateGenerateCourseRequest } from '../utils/validation.js';
import { sendErrorResponse, handleLLMError } from '../utils/errors.js';

export function createCoursesRoutes(dbHelper: DatabaseHelper, ai: GoogleGenAI): Router {
  const router = Router();

  // Generate course content
  const generateCourse: RouteHandler<GenerateCourseRequest, GenerateCourseResponse> = async (req, res) => {
    try {
      const { topic, subjectId, topicId } = validateGenerateCourseRequest(req.body);

      // Construct the meta-prompt
      const metaPrompt = `**Your Role:** You are an expert Qt tutor and Python developer. Your name is QtMaster<PERSON>.
**Your Task:** Generate a complete, self-contained, and highly practical learning module for a user trying to master Qt with Python. The user wants to learn about the topic: **"${topic}"**.
**CRITICAL INSTRUCTION FOR UNIQUENESS:** This is a mastery-based learning application. The user will request lessons on the same topic repeatedly. You **MUST NOT** repeat content. For every request, you must generate a **completely new and distinct module**. To achieve this, you must:
- Use novel analogies and different explanatory approaches.
- Create entirely new, runnable code examples that illustrate the concept from a different perspective.
- Design a unique, practical task or exercise that is different from any you have created before.
**Output Format:**
You **MUST** format your entire response in Markdown. The structure must be as follows:
##  Module: ${topic}
### 💡 The "Why": Introduction & Relevance
(Provide a fresh, engaging introduction explaining why this topic is essential in Qt development.)
### ⚙️ The "What": Core Concepts Explained
(Break down the core principles of the topic using new analogies and explanations. Do not repeat previous explanations.)
### 💻 The "How": New Code Example (PySide6)
(Provide a complete, well-commented, and runnable PySide6 code example that is completely different from any previous example for this topic.)
### ✅ Your New Task: Hands-On Practice
(Design a new, specific, and practical task that requires the user to write code to apply what they've learned in this module. The task must be unique.)`;

      const llmResponse = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: metaPrompt,
        config: {
          thinkingConfig: {
            thinkingBudget: 0, // Disables thinking
          },
        },
      });

      // Extract the content from the LLM response
      const content = llmResponse.text || '';

      // Save the generated course content if subjectId and topicId are provided
      if (subjectId && topicId) {
        try {
          const courseId = uuidv4();
          await dbHelper.run(`
            INSERT INTO courses (id, subject_id, topic_id, topic_title, content)
            VALUES (?, ?, ?, ?, ?)
          `, [courseId, subjectId, topicId, topic, content]);
        } catch (dbError) {
          console.error("Error saving course content:", dbError);
          // Continue without failing the request
        }
      }

      // Send the content back to the frontend
      res.json({ content });
    } catch (error) {
      if (error instanceof Error) {
        // Handle LLM-specific errors
        if (error.name === 'LLMApiError') {
          const llmError = handleLLMError(error as any);
          sendErrorResponse(res, llmError);
        } else {
          sendErrorResponse(res, error);
        }
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Register routes
  router.post('/generate', generateCourse);

  return router;
}
