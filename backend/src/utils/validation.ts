import { 
  GenerateCurriculumRequest, 
  GenerateCourseRequest, 
  CreateSubjectRequest, 
  UpdateSubjectRequest, 
  CreateCurriculumRequest 
} from '../types/index.js';

/**
 * Validation error class
 */
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Validate generate curriculum request
 */
export function validateGenerateCurriculumRequest(body: any): GenerateCurriculumRequest {
  if (!body || typeof body !== 'object') {
    throw new ValidationError('Request body must be an object');
  }

  if (!body.subject || typeof body.subject !== 'string') {
    throw new ValidationError('Subject is required and must be a string');
  }

  if (body.subject.trim().length === 0) {
    throw new ValidationError('Subject cannot be empty');
  }

  if (body.subject.length > 200) {
    throw new ValidationError('Subject must be less than 200 characters');
  }

  return {
    subject: body.subject.trim()
  };
}

/**
 * Validate generate course request
 */
export function validateGenerateCourseRequest(body: any): GenerateCourseRequest {
  if (!body || typeof body !== 'object') {
    throw new ValidationError('Request body must be an object');
  }

  if (!body.topic || typeof body.topic !== 'string') {
    throw new ValidationError('Topic is required and must be a string');
  }

  if (body.topic.trim().length === 0) {
    throw new ValidationError('Topic cannot be empty');
  }

  if (body.topic.length > 200) {
    throw new ValidationError('Topic must be less than 200 characters');
  }

  const validated: GenerateCourseRequest = {
    topic: body.topic.trim()
  };

  if (body.subjectId) {
    if (typeof body.subjectId !== 'string') {
      throw new ValidationError('Subject ID must be a string');
    }
    validated.subjectId = body.subjectId;
  }

  if (body.topicId) {
    if (typeof body.topicId !== 'string') {
      throw new ValidationError('Topic ID must be a string');
    }
    validated.topicId = body.topicId;
  }

  if (body.apiKey) {
    if (typeof body.apiKey !== 'string') {
      throw new ValidationError('API key must be a string');
    }
    validated.apiKey = body.apiKey;
  }

  return validated;
}

/**
 * Validate create subject request
 */
export function validateCreateSubjectRequest(body: any): CreateSubjectRequest {
  if (!body || typeof body !== 'object') {
    throw new ValidationError('Request body must be an object');
  }

  if (!body.title || typeof body.title !== 'string') {
    throw new ValidationError('Title is required and must be a string');
  }

  if (body.title.trim().length === 0) {
    throw new ValidationError('Title cannot be empty');
  }

  if (body.title.length > 100) {
    throw new ValidationError('Title must be less than 100 characters');
  }

  const validated: CreateSubjectRequest = {
    title: body.title.trim()
  };

  if (body.description !== undefined) {
    if (typeof body.description !== 'string') {
      throw new ValidationError('Description must be a string');
    }
    if (body.description.length > 500) {
      throw new ValidationError('Description must be less than 500 characters');
    }
    validated.description = body.description.trim();
  }

  return validated;
}

/**
 * Validate update subject request
 */
export function validateUpdateSubjectRequest(body: any): UpdateSubjectRequest {
  return validateCreateSubjectRequest(body); // Same validation rules
}

/**
 * Validate create curriculum request
 */
export function validateCreateCurriculumRequest(body: any): CreateCurriculumRequest {
  if (!body || typeof body !== 'object') {
    throw new ValidationError('Request body must be an object');
  }

  if (!body.subjectId || typeof body.subjectId !== 'string') {
    throw new ValidationError('Subject ID is required and must be a string');
  }

  if (!body.structure || typeof body.structure !== 'object') {
    throw new ValidationError('Structure is required and must be an object');
  }

  if (!body.structure.curriculum || !Array.isArray(body.structure.curriculum)) {
    throw new ValidationError('Structure must contain a curriculum array');
  }

  return {
    subjectId: body.subjectId,
    structure: body.structure
  };
}

/**
 * Validate UUID format
 */
export function validateUUID(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Validate ID parameter
 */
export function validateIdParam(id: string): string {
  if (!id || typeof id !== 'string') {
    throw new ValidationError('ID parameter is required and must be a string');
  }

  if (!validateUUID(id)) {
    throw new ValidationError('ID must be a valid UUID');
  }

  return id;
}
