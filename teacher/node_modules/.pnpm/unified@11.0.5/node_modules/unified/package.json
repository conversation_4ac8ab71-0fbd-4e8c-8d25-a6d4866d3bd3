{"name": "unified", "version": "11.0.5", "description": "parse, inspect, transform, and serialize content through syntax trees", "license": "MIT", "keywords": ["ast", "compile", "content", "cst", "parse", "process", "rehype", "remark", "retext", "serialize", "stringify", "syntax", "transform", "tree", "unified"], "homepage": "https://unifiedjs.com", "repository": "unifiedjs/unified", "bugs": "https://github.com/unifiedjs/unified/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Vse Mozhet Byt <<EMAIL>>", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "bail": "^2.0.0", "devlop": "^1.0.0", "extend": "^3.0.0", "is-plain-obj": "^4.0.0", "trough": "^2.0.0", "vfile": "^6.0.0"}, "devDependencies": {"@types/extend": "^3.0.0", "@types/hast": "^3.0.0", "@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "tsd": "^0.31.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.58.0"}, "scripts": {"build": "tsc --build --clean && tsc --build && node script/fix-types.js && type-coverage && tsd", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "prepack": "npm run build && npm run format", "test": "npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --check-coverage --reporter lcov npm run test-api"}, "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", ["remark-lint-no-html", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/naming-convention": "off", "import/no-duplicates": "off"}}], "prettier": true, "rules": {"unicorn/no-this-assignment": "off"}}}