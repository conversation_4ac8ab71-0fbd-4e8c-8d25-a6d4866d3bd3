/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M16 12H3", key: "1a2rj7" }],
  ["path", { d: "M16 18H3", key: "12xzn7" }],
  ["path", { d: "M16 6H3", key: "1wxfjs" }],
  ["path", { d: "M21 12h.01", key: "msek7k" }],
  ["path", { d: "M21 18h.01", key: "1e8rq1" }],
  ["path", { d: "M21 6h.01", key: "1koanj" }]
];
const TableOfContents = createLucideIcon("table-of-contents", __iconNode);

export { __iconNode, TableOfContents as default };
//# sourceMappingURL=table-of-contents.js.map
