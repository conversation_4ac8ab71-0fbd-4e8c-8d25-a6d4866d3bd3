{"name": "trim-lines", "version": "3.0.1", "description": "Remove spaces and tabs around line-breaks", "license": "MIT", "keywords": ["space", "tab", "line", "break", "trim"], "repository": "wooorm/trim-lines", "bugs": "https://github.com/wooorm/trim-lines/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "devDependencies": {"@types/tape": "^4.0.0", "c8": "^7.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "rimraf": "^3.0.0", "tape": "^5.0.0", "typescript": "^4.0.0", "xo": "^0.50.0"}, "scripts": {"prepublishOnly": "npm run build && npm run format", "prebuild": "rimraf \"*.d.ts\"", "build": "tsc", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test.js", "test-coverage": "c8 --check-coverage --branches 100 --functions 100 --lines 100 --statements 100 --reporter lcov node test.js", "test": "npm run build && npm run format && npm run test-coverage"}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}}