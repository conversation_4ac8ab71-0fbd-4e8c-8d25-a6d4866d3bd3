{"version": 3, "file": "style-to-object.js", "sources": ["../node_modules/inline-style-parser/index.js", "../src/index.ts"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", null], "names": [], "mappings": ";;;;;;;;;;;;;;;;CAAA;CACA;EACA,IAAI,aAAa,GAAG,iCAAiC;;EAErD,IAAI,aAAa,GAAG,KAAK;EACzB,IAAI,gBAAgB,GAAG,MAAM;;CAE7B;EACA,IAAI,cAAc,GAAG,wCAAwC;EAC7D,IAAI,WAAW,GAAG,OAAO;EACzB,IAAI,WAAW,GAAG,sDAAsD;EACxE,IAAI,eAAe,GAAG,SAAS;;CAE/B;EACA,IAAI,UAAU,GAAG,YAAY;;CAE7B;EACA,IAAI,OAAO,GAAG,IAAI;EAClB,IAAI,aAAa,GAAG,GAAG;EACvB,IAAI,QAAQ,GAAG,GAAG;EAClB,IAAI,YAAY,GAAG,EAAE;;CAErB;EACA,IAAI,YAAY,GAAG,SAAS;EAC5B,IAAI,gBAAgB,GAAG,aAAa;;CAEpC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;CAC3C,GAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACjC,KAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC;CAC1D;;CAEA,GAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;;CAEvB,GAAE,OAAO,GAAG,OAAO,IAAI,EAAE;;CAEzB;CACA;CACA;IACE,IAAI,MAAM,GAAG,CAAC;IACd,IAAI,MAAM,GAAG,CAAC;;CAEhB;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,cAAc,CAAC,GAAG,EAAE;MAC3B,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;CACxC,KAAI,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,MAAM;MACjC,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;CACpC,KAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM;CACtD;;CAEA;CACA;CACA;CACA;CACA;IACE,SAAS,QAAQ,GAAG;MAClB,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;MAC5C,OAAO,UAAU,IAAI,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC;CACzC,OAAM,UAAU,EAAE;CAClB,OAAM,OAAO,IAAI;OACZ;CACL;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK;CACtB,KAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;CAC/C,KAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;CAChC;;CAEA;CACA;CACA;CACA,GAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK;;CAIpC;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,GAAG,EAAE;CACtB,KAAI,IAAI,GAAG,GAAG,IAAI,KAAK;CACvB,OAAM,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG;OACvD;CACL,KAAI,GAAG,CAAC,MAAM,GAAG,GAAG;CACpB,KAAI,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM;CACjC,KAAI,GAAG,CAAC,IAAI,GAAG,MAAM;CACrB,KAAI,GAAG,CAAC,MAAM,GAAG,MAAM;CACvB,KAAI,GAAG,CAAC,MAAM,GAAG,KAAK;;CAEtB,KAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAEnB,MAAM;CACX,OAAM,MAAM,GAAG;CACf;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,EAAE,EAAE;MACjB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;MACtB,IAAI,CAAC,CAAC,EAAE;CACZ,KAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MACd,cAAc,CAAC,GAAG,CAAC;MACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;CACnC,KAAI,OAAO,CAAC;CACZ;;CAEA;CACA;CACA;IACE,SAAS,UAAU,GAAG;MACpB,KAAK,CAAC,gBAAgB,CAAC;CAC3B;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,KAAI,IAAI,CAAC;CACT,KAAI,KAAK,GAAG,KAAK,IAAI,EAAE;CACvB,KAAI,QAAQ,CAAC,GAAG,OAAO,EAAE,GAAG;CAC5B,OAAM,IAAI,CAAC,KAAK,KAAK,EAAE;CACvB,SAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;CACrB;CACA;CACA,KAAI,OAAO,KAAK;CAChB;;CAEA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,OAAO,GAAG;CACrB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;CACxB,KAAI,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;;MAErE,IAAI,CAAC,GAAG,CAAC;MACT;CACJ,OAAM,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,QAAO,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACpE;CACN,OAAM,EAAE,CAAC;CACT;MACI,CAAC,IAAI,CAAC;;MAEN,IAAI,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CAC9C,OAAM,OAAO,KAAK,CAAC,wBAAwB,CAAC;CAC5C;;CAEA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;MAC/B,MAAM,IAAI,CAAC;MACX,cAAc,CAAC,GAAG,CAAC;CACvB,KAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;MACtB,MAAM,IAAI,CAAC;;MAEX,OAAO,GAAG,CAAC;QACT,IAAI,EAAE,YAAY;CACxB,OAAM,OAAO,EAAE;CACf,MAAK,CAAC;CACN;;CAEA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,WAAW,GAAG;CACzB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;;CAExB;CACA,KAAI,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC;MAChC,IAAI,CAAC,IAAI,EAAE;CACf,KAAI,OAAO,EAAE;;CAEb;MACI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,KAAK,CAAC,sBAAsB,CAAC;;CAEjE;CACA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC;;CAEhC,KAAI,IAAI,GAAG,GAAG,GAAG,CAAC;QACZ,IAAI,EAAE,gBAAgB;CAC5B,OAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;CAClE,OAAM,KAAK,EAAE;CACb,WAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC;YAChD;CACV,MAAK,CAAC;;CAEN;MACI,KAAK,CAAC,eAAe,CAAC;;CAE1B,KAAI,OAAO,GAAG;CACd;;CAEA;CACA;CACA;CACA;CACA;IACE,SAAS,YAAY,GAAG;MACtB,IAAI,KAAK,GAAG,EAAE;;MAEd,QAAQ,CAAC,KAAK,CAAC;;CAEnB;CACA,KAAI,IAAI,IAAI;CACZ,KAAI,QAAQ,IAAI,GAAG,WAAW,EAAE,GAAG;CACnC,OAAM,IAAI,IAAI,KAAK,KAAK,EAAE;CAC1B,SAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;UAChB,QAAQ,CAAC,KAAK,CAAC;CACvB;CACA;;CAEA,KAAI,OAAO,KAAK;CAChB;;CAEA,GAAE,UAAU,EAAE;IACZ,OAAO,YAAY,EAAE;GACtB;;CAED;CACA;CACA;CACA;CACA;CACA;EACA,SAAS,IAAI,CAAC,GAAG,EAAE;CACnB,GAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,YAAY;CACnE;;;;;;;CCrPA;;;;;;;;;;;;;CAaG;CACW,SAAU,aAAa,CACnC,KAAa,EACb,QAAmB,EAAA;KAEnB,IAAI,WAAW,GAAuB,IAAI;KAE1C,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACvC,QAAA,OAAO,WAAW;;CAGpB,IAAA,IAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;CACjC,IAAA,IAAM,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU;CAElD,IAAA,YAAY,CAAC,OAAO,CAAC,UAAC,WAAW,EAAA;CAC/B,QAAA,IAAI,WAAW,CAAC,IAAI,KAAK,aAAa,EAAE;aACtC;;SAGM,IAAA,QAAQ,GAAY,WAAW,CAAA,QAAvB,EAAE,KAAK,GAAK,WAAW,CAAA,KAAhB;SAEvB,IAAI,WAAW,EAAE;CACf,YAAA,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;;cACjC,IAAI,KAAK,EAAE;CAChB,YAAA,WAAW,GAAG,WAAW,IAAI,EAAE;CAC/B,YAAA,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK;;CAEjC,KAAC,CAAC;CAEF,IAAA,OAAO,WAAW;CACpB;;;;;;;;", "x_google_ignoreList": [0]}