{"name": "style-to-js", "version": "1.1.17", "description": "Parses CSS inline style to JavaScript object (camelCased).", "author": "<PERSON> <<EMAIL>>", "main": "cjs/index.js", "scripts": {"build": "npm run build:cjs && npm run build:umd", "build:cjs": "tsc --declaration --outDir cjs", "build:umd": "rollup --config --failAfterWarnings", "clean": "rm -rf cjs umd", "lint": "eslint .", "lint:tsc": "tsc --noEmit", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "prepublishOnly": "npm run lint && npm run lint:tsc && npm run test:ci && npm run clean && npm run build", "test": "jest", "test:ci": "CI=true jest --ci --colors --coverage", "test:watch": "jest --watch"}, "repository": {"type": "git", "url": "git+https://github.com/remarkablemark/style-to-js.git"}, "bugs": {"url": "https://github.com/remarkablemark/style-to-js/issues"}, "keywords": ["style-to-js", "css", "style", "javascript", "object", "pojo"], "dependencies": {"style-to-object": "1.0.9"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/compat": "1.3.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.29.0", "@rollup/plugin-commonjs": "28.0.5", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/plugin-terser": "0.4.4", "@types/jest": "29.5.14", "@typescript-eslint/eslint-plugin": "8.34.0", "@typescript-eslint/parser": "8.34.0", "eslint": "9.29.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-simple-import-sort": "12.1.1", "globals": "16.2.0", "husky": "9.1.7", "jest": "30.0.0", "lint-staged": "16.1.2", "prettier": "3.5.3", "rollup": "4.43.0", "ts-jest": "29.4.0", "ts-node": "10.9.2", "typescript": "5.8.3"}, "files": ["cjs/", "src/", "umd/"], "license": "MIT"}