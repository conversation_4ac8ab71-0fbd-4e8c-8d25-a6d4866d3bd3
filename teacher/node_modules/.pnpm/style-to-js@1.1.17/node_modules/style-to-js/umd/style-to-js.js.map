{"version": 3, "file": "style-to-js.js", "sources": ["../node_modules/inline-style-parser/index.js", "../node_modules/style-to-object/cjs/index.js", "../cjs/utilities.js", "../cjs/index.js", "../cjs/index.js?commonjs-entry"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = StyleToObject;\nvar inline_style_parser_1 = __importDefault(require(\"inline-style-parser\"));\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nfunction StyleToObject(style, iterator) {\n    var styleObject = null;\n    if (!style || typeof style !== 'string') {\n        return styleObject;\n    }\n    var declarations = (0, inline_style_parser_1.default)(style);\n    var hasIterator = typeof iterator === 'function';\n    declarations.forEach(function (declaration) {\n        if (declaration.type !== 'declaration') {\n            return;\n        }\n        var property = declaration.property, value = declaration.value;\n        if (hasIterator) {\n            iterator(property, value, declaration);\n        }\n        else if (value) {\n            styleObject = styleObject || {};\n            styleObject[property] = value;\n        }\n    });\n    return styleObject;\n}\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n    return !property ||\n        NO_HYPHEN_REGEX.test(property) ||\n        CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) { return \"\".concat(prefix, \"-\"); };\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n    if (options === void 0) { options = {}; }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;\n//# sourceMappingURL=utilities.js.map", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar style_to_object_1 = __importDefault(require(\"style-to-object\"));\nvar utilities_1 = require(\"./utilities\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== 'string') {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function (property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;\n//# sourceMappingURL=index.js.map", "import { getDefaultExportFromCjs } from \"\u0000commonjsHelpers.js\";\nimport { __require as requireCjs } from \"/home/<USER>/work/style-to-js/style-to-js/cjs/index.js\";\nvar cjsExports = requireCjs();\nexport { cjsExports as __moduleExports };\nexport default /*@__PURE__*/getDefaultExportFromCjs(cjsExports);"], "names": ["this", "cjs", "require$$0", "require$$1"], "mappings": ";;;;;;;;;;;;;;;;;;CAAA;CACA;EACA,IAAI,aAAa,GAAG,iCAAiC;;EAErD,IAAI,aAAa,GAAG,KAAK;EACzB,IAAI,gBAAgB,GAAG,MAAM;;CAE7B;EACA,IAAI,cAAc,GAAG,wCAAwC;EAC7D,IAAI,WAAW,GAAG,OAAO;EACzB,IAAI,WAAW,GAAG,sDAAsD;EACxE,IAAI,eAAe,GAAG,SAAS;;CAE/B;EACA,IAAI,UAAU,GAAG,YAAY;;CAE7B;EACA,IAAI,OAAO,GAAG,IAAI;EAClB,IAAI,aAAa,GAAG,GAAG;EACvB,IAAI,QAAQ,GAAG,GAAG;EAClB,IAAI,YAAY,GAAG,EAAE;;CAErB;EACA,IAAI,YAAY,GAAG,SAAS;EAC5B,IAAI,gBAAgB,GAAG,aAAa;;CAEpC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;CAC3C,GAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACjC,KAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC;CAC1D;;CAEA,GAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;;CAEvB,GAAE,OAAO,GAAG,OAAO,IAAI,EAAE;;CAEzB;CACA;CACA;IACE,IAAI,MAAM,GAAG,CAAC;IACd,IAAI,MAAM,GAAG,CAAC;;CAEhB;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,cAAc,CAAC,GAAG,EAAE;MAC3B,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;CACxC,KAAI,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,MAAM;MACjC,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;CACpC,KAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM;CACtD;;CAEA;CACA;CACA;CACA;CACA;IACE,SAAS,QAAQ,GAAG;MAClB,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;MAC5C,OAAO,UAAU,IAAI,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC;CACzC,OAAM,UAAU,EAAE;CAClB,OAAM,OAAO,IAAI;OACZ;CACL;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK;CACtB,KAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;CAC/C,KAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;CAChC;;CAEA;CACA;CACA;CACA,GAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK;;CAIpC;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,GAAG,EAAE;CACtB,KAAI,IAAI,GAAG,GAAG,IAAI,KAAK;CACvB,OAAM,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG;OACvD;CACL,KAAI,GAAG,CAAC,MAAM,GAAG,GAAG;CACpB,KAAI,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM;CACjC,KAAI,GAAG,CAAC,IAAI,GAAG,MAAM;CACrB,KAAI,GAAG,CAAC,MAAM,GAAG,MAAM;CACvB,KAAI,GAAG,CAAC,MAAM,GAAG,KAAK;;CAEtB,KAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAEnB,MAAM;CACX,OAAM,MAAM,GAAG;CACf;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,KAAK,CAAC,EAAE,EAAE;MACjB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;MACtB,IAAI,CAAC,CAAC,EAAE;CACZ,KAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MACd,cAAc,CAAC,GAAG,CAAC;MACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;CACnC,KAAI,OAAO,CAAC;CACZ;;CAEA;CACA;CACA;IACE,SAAS,UAAU,GAAG;MACpB,KAAK,CAAC,gBAAgB,CAAC;CAC3B;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,KAAI,IAAI,CAAC;CACT,KAAI,KAAK,GAAG,KAAK,IAAI,EAAE;CACvB,KAAI,QAAQ,CAAC,GAAG,OAAO,EAAE,GAAG;CAC5B,OAAM,IAAI,CAAC,KAAK,KAAK,EAAE;CACvB,SAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;CACrB;CACA;CACA,KAAI,OAAO,KAAK;CAChB;;CAEA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,OAAO,GAAG;CACrB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;CACxB,KAAI,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;;MAErE,IAAI,CAAC,GAAG,CAAC;MACT;CACJ,OAAM,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,QAAO,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACpE;CACN,OAAM,EAAE,CAAC;CACT;MACI,CAAC,IAAI,CAAC;;MAEN,IAAI,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CAC9C,OAAM,OAAO,KAAK,CAAC,wBAAwB,CAAC;CAC5C;;CAEA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;MAC/B,MAAM,IAAI,CAAC;MACX,cAAc,CAAC,GAAG,CAAC;CACvB,KAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;MACtB,MAAM,IAAI,CAAC;;MAEX,OAAO,GAAG,CAAC;QACT,IAAI,EAAE,YAAY;CACxB,OAAM,OAAO,EAAE;CACf,MAAK,CAAC;CACN;;CAEA;CACA;CACA;CACA;CACA;CACA;IACE,SAAS,WAAW,GAAG;CACzB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;;CAExB;CACA,KAAI,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC;MAChC,IAAI,CAAC,IAAI,EAAE;CACf,KAAI,OAAO,EAAE;;CAEb;MACI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,KAAK,CAAC,sBAAsB,CAAC;;CAEjE;CACA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC;;CAEhC,KAAI,IAAI,GAAG,GAAG,GAAG,CAAC;QACZ,IAAI,EAAE,gBAAgB;CAC5B,OAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;CAClE,OAAM,KAAK,EAAE;CACb,WAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC;YAChD;CACV,MAAK,CAAC;;CAEN;MACI,KAAK,CAAC,eAAe,CAAC;;CAE1B,KAAI,OAAO,GAAG;CACd;;CAEA;CACA;CACA;CACA;CACA;IACE,SAAS,YAAY,GAAG;MACtB,IAAI,KAAK,GAAG,EAAE;;MAEd,QAAQ,CAAC,KAAK,CAAC;;CAEnB;CACA,KAAI,IAAI,IAAI;CACZ,KAAI,QAAQ,IAAI,GAAG,WAAW,EAAE,GAAG;CACnC,OAAM,IAAI,IAAI,KAAK,KAAK,EAAE;CAC1B,SAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;UAChB,QAAQ,CAAC,KAAK,CAAC;CACvB;CACA;;CAEA,KAAI,OAAO,KAAK;CAChB;;CAEA,GAAE,UAAU,EAAE;IACZ,OAAO,YAAY,EAAE;GACtB;;CAED;CACA;CACA;CACA;CACA;CACA;EACA,SAAS,IAAI,CAAC,GAAG,EAAE;CACnB,GAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,YAAY;CACnE;;;;;;;;;ECnQA,IAAI,eAAe,GAAG,CAACA,KAAI,IAAIA,KAAI,CAAC,eAAe,KAAK,UAAU,GAAG,EAAE;CACvE,KAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;GAC5D;CACD,CAAA,MAAM,CAAC,cAAc,CAACC,KAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;CAC7D,CAAAA,KAAA,CAAA,OAAe,GAAG,aAAa;CAC/B,CAAA,IAAI,qBAAqB,GAAG,eAAe,CAACC,0BAA8B,CAAC;CAC3E;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE;MACpC,IAAI,WAAW,GAAG,IAAI;MACtB,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CAC7C,SAAQ,OAAO,WAAW;CAC1B;MACI,IAAI,YAAY,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC;CAChE,KAAI,IAAI,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU;CACpD,KAAI,YAAY,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;CAChD,SAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,aAAa,EAAE;cACpC;CACZ;UACQ,IAAI,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK;UAC9D,IAAI,WAAW,EAAE;CACzB,aAAY,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;CAClD;eACa,IAAI,KAAK,EAAE;CACxB,aAAY,WAAW,GAAG,WAAW,IAAI,EAAE;CAC3C,aAAY,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK;CACzC;CACA,MAAK,CAAC;CACN,KAAI,OAAO,WAAW;CACtB;CACA;;;;;;;;;;;CC1CA,CAAA,MAAM,CAAC,cAAc,CAAC,SAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;CAC7D,CAAiB,SAAA,CAAA,SAAA,GAAG,MAAM;EAC1B,IAAI,qBAAqB,GAAG,oBAAoB;EAChD,IAAI,YAAY,GAAG,WAAW;EAC9B,IAAI,eAAe,GAAG,SAAS;EAC/B,IAAI,mBAAmB,GAAG,4BAA4B;EACtD,IAAI,sBAAsB,GAAG,SAAS;CACtC;CACA;CACA;CACA,CAAA,IAAI,aAAa,GAAG,UAAU,QAAQ,EAAE;MACpC,OAAO,CAAC,QAAQ;CACpB,SAAQ,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;CACtC,SAAQ,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC;GAC3C;CACD;CACA;CACA;CACA,CAAA,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,SAAS,EAAE;CAC7C,KAAI,OAAO,SAAS,CAAC,WAAW,EAAE;GACjC;CACD;CACA;CACA;CACA,CAAA,IAAI,UAAU,GAAG,UAAU,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;CAC5E;CACA;CACA;CACA,CAAA,IAAI,SAAS,GAAG,UAAU,QAAQ,EAAE,OAAO,EAAE;MACzC,IAAI,OAAO,KAAK,MAAM,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC;CAC3C,KAAI,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;CACjC,SAAQ,OAAO,QAAQ;CACvB;CACA,KAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE;CACrC,KAAI,IAAI,OAAO,CAAC,WAAW,EAAE;CAC7B;UACQ,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC;CACvE;WACS;CACT;UACQ,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC;CACpE;MACI,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;GACpD;CACD,CAAA,SAAA,CAAA,SAAiB,GAAG,SAAS;CAC7B;;;;;;;;;;EC7CA,IAAI,eAAe,GAAG,CAACF,GAAI,IAAIA,GAAI,CAAC,eAAe,KAAK,UAAU,GAAG,EAAE;CACvE,KAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;GAC5D;CACD,CAAA,IAAI,iBAAiB,GAAG,eAAe,CAACE,cAA0B,CAAC;EACnE,IAAI,WAAW,GAAGC,gBAAsB,EAAA;CACxC;CACA;CACA;CACA,CAAA,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;MAC/B,IAAI,MAAM,GAAG,EAAE;MACf,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CAC7C,SAAQ,OAAO,MAAM;CACrB;CACA,KAAI,IAAI,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,QAAQ,EAAE,KAAK,EAAE;CACrE;CACA,SAAQ,IAAI,QAAQ,IAAI,KAAK,EAAE;CAC/B,aAAY,MAAM,CAAC,IAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,GAAG,KAAK;CACzE;CACA,MAAK,CAAC;CACN,KAAI,OAAO,MAAM;CACjB;EACA,SAAS,CAAC,OAAO,GAAG,SAAS;CAC7B,CAAA,GAAc,GAAG,SAAS;CAC1B;;;;CCtBA,IAAI,UAAU,GAAG,UAAU,EAAE;AAE7B,aAAe,aAAa,uBAAuB,CAAC,UAAU,CAAC;;;;;;;;", "x_google_ignoreList": [0, 1]}