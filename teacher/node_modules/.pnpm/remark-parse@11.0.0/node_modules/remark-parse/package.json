{"name": "remark-parse", "version": "11.0.0", "description": "remark plugin to add support for parsing markdown input", "license": "MIT", "keywords": ["abstract", "ast", "markdown", "mdast", "parse", "plugin", "remark", "remark-plugin", "syntax", "tree", "unified"], "homepage": "https://remark.js.org", "repository": "https://github.com/remarkjs/remark/tree/main/packages/remark-parse", "bugs": "https://github.com/remarkjs/remark/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <elija<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-from-markdown": "^2.0.0", "micromark-util-types": "^2.0.0", "unified": "^11.0.0"}, "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/consistent-type-definitions": "off"}}], "prettier": true, "rules": {"unicorn/no-this-assignment": "off"}}}