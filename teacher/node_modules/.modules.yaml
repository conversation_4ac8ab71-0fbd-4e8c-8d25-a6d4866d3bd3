hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.9':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.9':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.9':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.9':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.9':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.9':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.9':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.9':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.9':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.9':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.9':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.9':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.9':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.9':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.9':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.9':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.9':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.9':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.9':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.9':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.9':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.9':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.9':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.9':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.9':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.33.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': public
  '@eslint/core@0.15.2':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rolldown/pluginutils@1.0.0-beta.32':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.46.3':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.3':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.3':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.3':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.3':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.3':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.3':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.3':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.3':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.3':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.3':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.3':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.3':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.3':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.3':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.3':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.3':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.3':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.3':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.3':
    '@rollup/rollup-win32-x64-msvc': private
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.12':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.12':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.12':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.12':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.12':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.12':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.12':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.12':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.12':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/eslint-plugin@8.40.0(@typescript-eslint/parser@8.40.0(eslint@9.33.0(jiti@2.5.1))(typescript@5.8.3))(eslint@9.33.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.40.0(eslint@9.33.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.40.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.40.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.40.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.40.0(eslint@9.33.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.40.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.40.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.40.0(eslint@9.33.0(jiti@2.5.1))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.40.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  asynckit@0.4.0:
    asynckit: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.3:
    browserslist: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001735:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chownr@3.0.0:
    chownr: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  concat-map@0.0.1:
    concat-map: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.207:
    electron-to-chromium: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.9:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@6.0.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-decimal@2.0.1:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  isexe@2.0.0:
    isexe: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  property-information@7.1.0:
    property-information: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@7.8.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    react-router: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rollup@4.46.3:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  supports-color@7.2.0:
    supports-color: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  unified@11.0.5:
    unified: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  update-browserslist-db@1.1.3(browserslist@4.25.3):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yallist@5.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.3
pendingBuilds: []
prunedAt: Tue, 19 Aug 2025 11:28:21 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@esbuild/win32-x64@0.25.9'
  - '@rollup/rollup-android-arm-eabi@4.46.3'
  - '@rollup/rollup-android-arm64@4.46.3'
  - '@rollup/rollup-darwin-arm64@4.46.3'
  - '@rollup/rollup-freebsd-arm64@4.46.3'
  - '@rollup/rollup-freebsd-x64@4.46.3'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.3'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.3'
  - '@rollup/rollup-linux-arm64-gnu@4.46.3'
  - '@rollup/rollup-linux-arm64-musl@4.46.3'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.3'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.3'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.3'
  - '@rollup/rollup-linux-riscv64-musl@4.46.3'
  - '@rollup/rollup-linux-s390x-gnu@4.46.3'
  - '@rollup/rollup-linux-x64-gnu@4.46.3'
  - '@rollup/rollup-linux-x64-musl@4.46.3'
  - '@rollup/rollup-win32-arm64-msvc@4.46.3'
  - '@rollup/rollup-win32-ia32-msvc@4.46.3'
  - '@rollup/rollup-win32-x64-msvc@4.46.3'
  - '@tailwindcss/oxide-android-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-arm64@4.1.12'
  - '@tailwindcss/oxide-freebsd-x64@4.1.12'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.12'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.12'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.12'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.12'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.12'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
