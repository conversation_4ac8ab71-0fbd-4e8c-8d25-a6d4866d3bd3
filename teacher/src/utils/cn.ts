import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge Tailwind CSS classes with proper precedence
 * @param inputs - Class values to merge
 * @returns Merged class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Common button variants for consistent styling
 */
export const buttonVariants = {
  primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  secondary: 'bg-dark-700 hover:bg-dark-600 text-dark-100 border border-dark-600 hover:border-dark-500 shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  accent: 'bg-accent-600 hover:bg-accent-700 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  success: 'bg-success-600 hover:bg-success-700 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  warning: 'bg-warning-600 hover:bg-warning-700 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  error: 'bg-error-600 hover:bg-error-700 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  ghost: 'bg-transparent hover:bg-dark-800 text-dark-200 hover:text-white border border-transparent hover:border-dark-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-900',
  outline: 'bg-transparent hover:bg-primary-600 text-primary-400 hover:text-white border border-primary-600 hover:border-primary-700 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900',
}

/**
 * Common button sizes
 */
export const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm font-medium rounded-md',
  md: 'px-4 py-2 text-sm font-medium rounded-lg',
  lg: 'px-6 py-3 text-base font-medium rounded-lg',
  xl: 'px-8 py-4 text-lg font-semibold rounded-xl',
}

/**
 * Common card variants
 */
export const cardVariants = {
  default: 'bg-dark-800 border border-dark-700 rounded-xl shadow-medium hover:shadow-large transition-all duration-300',
  elevated: 'bg-dark-800 border border-dark-700 rounded-xl shadow-large hover:shadow-glow transition-all duration-300 transform hover:scale-[1.02]',
  glass: 'bg-dark-800/80 backdrop-blur-xl border border-dark-700/50 rounded-xl shadow-medium hover:shadow-large transition-all duration-300',
  gradient: 'bg-gradient-to-br from-dark-800 to-dark-900 border border-dark-700 rounded-xl shadow-medium hover:shadow-large transition-all duration-300',
}

/**
 * Common input variants
 */
export const inputVariants = {
  default: 'w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg text-dark-100 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200',
  error: 'w-full px-4 py-3 bg-dark-800 border border-error-500 rounded-lg text-dark-100 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-error-500 focus:border-transparent transition-all duration-200',
  success: 'w-full px-4 py-3 bg-dark-800 border border-success-500 rounded-lg text-dark-100 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-success-500 focus:border-transparent transition-all duration-200',
}

/**
 * Common text variants
 */
export const textVariants = {
  h1: 'text-4xl font-bold text-dark-50 tracking-tight',
  h2: 'text-3xl font-bold text-dark-50 tracking-tight',
  h3: 'text-2xl font-semibold text-dark-50 tracking-tight',
  h4: 'text-xl font-semibold text-dark-100',
  h5: 'text-lg font-medium text-dark-100',
  h6: 'text-base font-medium text-dark-100',
  body: 'text-base text-dark-200 leading-relaxed',
  small: 'text-sm text-dark-300',
  muted: 'text-sm text-dark-400',
  accent: 'text-primary-400 font-medium',
}

/**
 * Animation classes for consistent motion
 */
export const animations = {
  fadeIn: 'animate-fade-in',
  fadeInUp: 'animate-fade-in-up',
  fadeInDown: 'animate-fade-in-down',
  slideInRight: 'animate-slide-in-right',
  slideInLeft: 'animate-slide-in-left',
  scaleIn: 'animate-scale-in',
  bounceSoft: 'animate-bounce-soft',
  pulseSoft: 'animate-pulse-soft',
  shimmer: 'animate-shimmer',
  float: 'animate-float',
  glow: 'animate-glow',
}

/**
 * Loading spinner component classes
 */
export const spinnerVariants = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
}
