import React from 'react';
import { motion } from 'framer-motion';
import { Settings, BookO<PERSON>, Sparkles } from 'lucide-react';
import { Button } from './ui';

function Header({ onSettingsClick }) {
  return (
    <motion.header
      className="sticky top-0 z-40 w-full border-b border-dark-700/50 bg-dark-900/80 backdrop-blur-xl"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <motion.div
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.02 }}
            transition={{ type: 'spring', stiffness: 400, damping: 17 }}
          >
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-glow">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <motion.div
                className="absolute -top-1 -right-1 w-4 h-4 bg-accent-400 rounded-full flex items-center justify-center"
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
              >
                <Sparkles className="w-2.5 h-2.5 text-white" />
              </motion.div>
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent">
                QtMaster.io
              </h1>
              <p className="text-xs text-dark-400 -mt-1">Learn. Master. Excel.</p>
            </div>
          </motion.div>

          {/* Settings Button */}
          <Button
            onClick={onSettingsClick}
            variant="ghost"
            size="md"
            icon={<Settings className="w-4 h-4" />}
            className="hover:bg-dark-800 hover:shadow-glow transition-all duration-200"
          >
            Settings
          </Button>
        </div>
      </div>
    </motion.header>
  );
}

export default Header;
