import React from 'react'
import { motion } from 'framer-motion'
import { cn, spinnerVariants } from '../../utils/cn'

interface LoadingSpinnerProps {
  size?: keyof typeof spinnerVariants
  className?: string
  text?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className,
  text 
}) => {
  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        {/* Primary spinner */}
        <motion.div
          className={cn(
            'border-4 border-dark-700 border-t-primary-500 rounded-full',
            spinnerVariants[size],
            className
          )}
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        />
        
        {/* Secondary spinner for enhanced effect */}
        <motion.div
          className={cn(
            'absolute inset-0 border-4 border-transparent border-t-secondary-500 rounded-full',
            spinnerVariants[size]
          )}
          animate={{ rotate: -360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          style={{ animationDelay: '0.15s' }}
        />
      </div>
      
      {text && (
        <motion.p
          className="text-dark-300 text-sm font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

export default LoadingSpinner
