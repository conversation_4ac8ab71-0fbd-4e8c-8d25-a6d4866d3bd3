import React, { useState } from 'react';
import { Key, Shield, ExternalLink } from 'lucide-react';
import { Modal, Button, Input } from './ui';

function SettingsModal({ onClose, onSave, initialApiKey }) {
  const [apiKey, setApiKey] = useState(initialApiKey || '');

  const handleSave = () => {
    onSave(apiKey);
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Settings"
      description="Configure your learning environment"
      size="md"
    >
      <div className="space-y-6">
        {/* API Key Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-500/10 rounded-lg flex items-center justify-center">
              <Key className="w-5 h-5 text-primary-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-dark-50">LLM API Key</h3>
              <p className="text-sm text-dark-300">Required for generating curricula and content</p>
            </div>
          </div>

          <Input
            id="apiKey"
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="Enter your API key"
            icon={<Key className="w-4 h-4" />}
          />

          <div className="bg-dark-800/50 border border-dark-700 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-success-400 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <p className="text-sm text-dark-200 font-medium">Privacy & Security</p>
                <p className="text-xs text-dark-300 leading-relaxed">
                  Your API key is stored locally in your browser and is never sent to any server except the LLM API.
                  We don't collect or store your API keys on our servers.
                </p>
                <a
                  href="https://makersuite.google.com/app/apikey"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-xs text-primary-400 hover:text-primary-300 transition-colors"
                >
                  Get your API key from Google AI Studio
                  <ExternalLink className="w-3 h-3" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-end pt-4 border-t border-dark-700">
          <Button onClick={onClose} variant="secondary" size="md">
            Cancel
          </Button>
          <Button onClick={handleSave} variant="primary" size="md">
            Save Settings
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SettingsModal;
