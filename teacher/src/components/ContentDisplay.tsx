import ReactMarkdown from 'react-markdown';

interface ContentDisplayProps {
  content: string;
  isLoading: boolean;
  selectedTopic: any;
}

function ContentDisplay({ content, isLoading, selectedTopic }: ContentDisplayProps) {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="w-12 h-12 border-4 border-gray-300 border-t-blue-400 rounded-full animate-spin"></div>
        <p className="mt-5 text-gray-400">Generating your course...</p>
      </div>
    );
  }

  if (!selectedTopic) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center text-gray-400">
        <h2 className="mb-4 text-blue-400">Welcome to QtMaster.io</h2>
        <p>Select a topic from the curriculum to get started.</p>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center text-gray-400">
        <h2 className="mb-4 text-blue-400">{selectedTopic.title}</h2>
        <p>Click "Generate New Course" to create a personalized learning module.</p>
      </div>
    );
  }

  return (
    <div className="text-gray-300 leading-relaxed [&_h1]:text-blue-400 [&_h1]:mt-6 [&_h1]:mb-4 [&_h2]:text-blue-400 [&_h2]:mt-6 [&_h2]:mb-4 [&_h3]:text-blue-400 [&_h3]:mt-6 [&_h3]:mb-4 [&_h4]:text-blue-400 [&_h4]:mt-6 [&_h4]:mb-4 [&_h5]:text-blue-400 [&_h5]:mt-6 [&_h5]:mb-4 [&_h6]:text-blue-400 [&_h6]:mt-6 [&_h6]:mb-4 [&_p]:mb-4 [&_pre]:bg-gray-800 [&_pre]:p-4 [&_pre]:rounded-lg [&_pre]:overflow-x-auto [&_pre]:mb-4 [&_code]:bg-blue-400/10 [&_code]:px-1 [&_code]:py-0.5 [&_code]:rounded [&_code]:font-mono [&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_ul]:mb-4 [&_ul]:pl-6 [&_ol]:mb-4 [&_ol]:pl-6 [&_li]:mb-2 [&_blockquote]:border-l-4 [&_blockquote]:border-blue-400 [&_blockquote]:pl-4 [&_blockquote]:my-4 [&_blockquote]:text-gray-400">
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
}

export default ContentDisplay;
