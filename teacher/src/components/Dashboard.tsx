import React, { useState, useEffect } from 'react';
import axios from 'axios';

function Dashboard({ onSubjectSelect, onCreateSubject }) {
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('http://localhost:3001/api/subjects');
      setSubjects(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching subjects:', error);
      setError('Failed to load subjects. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSubject = async (subjectId, subjectTitle) => {
    if (!window.confirm(`Are you sure you want to delete "${subjectTitle}"? This will also delete all associated curricula and courses.`)) {
      return;
    }

    try {
      await axios.delete(`http://localhost:3001/api/subjects/${subjectId}`);
      setSubjects(subjects.filter(subject => subject.id !== subjectId));
    } catch (error) {
      console.error('Error deleting subject:', error);
      alert('Failed to delete subject. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-slate-700 border-t-blue-500 rounded-full animate-spin"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-purple-500 rounded-full animate-spin" style={{ animationDelay: '0.15s' }}></div>
        </div>
        <p className="text-slate-300 text-lg font-medium animate-pulse">Loading your subjects...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6 text-center">
        <div className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div className="space-y-2">
          <h2 className="text-2xl font-semibold text-red-400">Something went wrong</h2>
          <p className="text-slate-300 max-w-md">{error}</p>
        </div>
        <button
          onClick={fetchSubjects}
          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Your Learning Subjects</h1>
        <button 
          className={styles.createButton}
          onClick={onCreateSubject}
        >
          + Create New Subject
        </button>
      </div>

      {subjects.length === 0 ? (
        <div className={styles.emptyState}>
          <h2>No subjects yet</h2>
          <p>Create your first learning subject to get started!</p>
          <button 
            className={styles.createButtonLarge}
            onClick={onCreateSubject}
          >
            Create Your First Subject
          </button>
        </div>
      ) : (
        <div className={styles.subjectsGrid}>
          {subjects.map((subject) => (
            <div key={subject.id} className={styles.subjectCard}>
              <div className={styles.subjectHeader}>
                <h3 className={styles.subjectTitle}>{subject.title}</h3>
                <div className={styles.subjectActions}>
                  <button
                    className={styles.deleteButton}
                    onClick={() => handleDeleteSubject(subject.id, subject.title)}
                    title="Delete subject"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              {subject.description && (
                <p className={styles.subjectDescription}>{subject.description}</p>
              )}
              
              <div className={styles.subjectMeta}>
                <span className={styles.curriculumStatus}>
                  {subject.has_curriculum ? '✓ Curriculum ready' : '⚠ No curriculum'}
                </span>
                <span className={styles.createdDate}>
                  Created {new Date(subject.created_at).toLocaleDateString()}
                </span>
              </div>
              
              <button
                className={styles.selectButton}
                onClick={() => onSubjectSelect(subject)}
                disabled={!subject.has_curriculum}
              >
                {subject.has_curriculum ? 'Start Learning' : 'Setup Required'}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default Dashboard;
