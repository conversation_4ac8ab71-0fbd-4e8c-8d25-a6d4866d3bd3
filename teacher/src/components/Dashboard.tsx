import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, BookOpen, Calendar, Trash2, AlertTriangle, CheckCircle } from 'lucide-react';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, LoadingSpinner } from './ui';

function Dashboard({ onSubjectSelect, onCreateSubject }) {
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('http://localhost:3001/api/subjects');
      setSubjects(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching subjects:', error);
      setError('Failed to load subjects. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSubject = async (subjectId, subjectTitle) => {
    if (!window.confirm(`Are you sure you want to delete "${subjectTitle}"? This will also delete all associated curricula and courses.`)) {
      return;
    }

    try {
      await axios.delete(`http://localhost:3001/api/subjects/${subjectId}`);
      setSubjects(subjects.filter(subject => subject.id !== subjectId));
    } catch (error) {
      console.error('Error deleting subject:', error);
      alert('Failed to delete subject. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center min-h-[60vh]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <LoadingSpinner size="xl" text="Loading your subjects..." />
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center min-h-[60vh] space-y-6 text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="w-20 h-20 bg-error-500/10 rounded-full flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: 'spring', stiffness: 200 }}
        >
          <AlertTriangle className="w-10 h-10 text-error-400" />
        </motion.div>
        <div className="space-y-2">
          <h2 className="text-2xl font-semibold text-error-400">Something went wrong</h2>
          <p className="text-dark-300 max-w-md">{error}</p>
        </div>
        <Button
          onClick={fetchSubjects}
          variant="primary"
          size="lg"
          className="mt-4"
        >
          Try Again
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 p-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 gap-4"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.5 }}
        >
          <div>
            <h1 className="text-4xl font-bold text-dark-50 tracking-tight mb-2">
              Your Learning Subjects
            </h1>
            <p className="text-dark-300 text-lg">
              Manage and explore your personalized learning journey
            </p>
          </div>
          <Button
            onClick={onCreateSubject}
            variant="primary"
            size="lg"
            icon={<Plus className="w-5 h-5" />}
            className="shadow-glow"
          >
            Create New Subject
          </Button>
        </motion.div>

        {subjects.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center min-h-[50vh] text-center space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="w-32 h-32 bg-primary-500/10 rounded-full flex items-center justify-center mb-4">
              <BookOpen className="w-16 h-16 text-primary-400" />
            </div>
            <div className="space-y-3">
              <h2 className="text-2xl font-semibold text-dark-100">No subjects yet</h2>
              <p className="text-dark-300 max-w-md">
                Create your first learning subject to get started on your educational journey!
              </p>
            </div>
            <Button
              onClick={onCreateSubject}
              variant="primary"
              size="xl"
              icon={<Plus className="w-5 h-5" />}
              className="mt-6 shadow-glow"
            >
              Create Your First Subject
            </Button>
          </motion.div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <AnimatePresence>
              {subjects.map((subject, index) => (
                <motion.div
                  key={subject.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                >
                  <Card variant="elevated" className="h-full">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-dark-50 mb-2">
                            {subject.title}
                          </CardTitle>
                          {subject.description && (
                            <CardDescription className="text-dark-300 line-clamp-2">
                              {subject.description}
                            </CardDescription>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSubject(subject.id, subject.title)}
                          className="p-2 hover:bg-error-500/10 hover:text-error-400 transition-colors"
                          title="Delete subject"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          {subject.has_curriculum ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-success-400" />
                              <span className="text-success-400 font-medium">Curriculum ready</span>
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="w-4 h-4 text-warning-400" />
                              <span className="text-warning-400 font-medium">No curriculum</span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 text-sm text-dark-400">
                        <Calendar className="w-4 h-4" />
                        <span>Created {new Date(subject.created_at).toLocaleDateString()}</span>
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button
                        onClick={() => onSubjectSelect(subject)}
                        disabled={!subject.has_curriculum}
                        variant={subject.has_curriculum ? "primary" : "secondary"}
                        size="md"
                        className="w-full"
                        icon={<BookOpen className="w-4 h-4" />}
                      >
                        {subject.has_curriculum ? 'Start Learning' : 'Setup Required'}
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}

export default Dashboard;
