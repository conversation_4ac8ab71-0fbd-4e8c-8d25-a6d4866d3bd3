import React, { useState } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, BookOpen, Sparkles, CheckCircle, AlertCircle, Edit3 } from 'lucide-react';
import CurriculumEditor from './CurriculumEditor.js';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent, Input, LoadingSpinner } from './ui';

function SubjectCreation({ onBack, onSubjectCreated, apiKey }) {
  const [step, setStep] = useState(1); // 1: topic input, 2: curriculum review, 3: curriculum editing
  const [subjectTitle, setSubjectTitle] = useState('');
  const [subjectDescription, setSubjectDescription] = useState('');
  const [generatedCurriculum, setGeneratedCurriculum] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);

  const handleGenerateCurriculum = async () => {
    if (!subjectTitle.trim()) {
      setError('Please enter a subject title');
      return;
    }

    if (!apiKey) {
      setError('Please set your API key in the settings');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await axios.post('http://localhost:3001/api/generate-curriculum', {
        subject: subjectTitle.trim()
      });

      setGeneratedCurriculum(response.data);
      setStep(2);
    } catch (error) {
      console.error('Error generating curriculum:', error);
      if (error.response) {
        setError(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        setError('Error: No response from server. Make sure the backend is running.');
      } else {
        setError(`Error: ${error.message}`);
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateSubject = async () => {
    try {
      // Create the subject
      const subjectResponse = await axios.post('http://localhost:3001/api/subjects', {
        title: subjectTitle.trim(),
        description: subjectDescription.trim()
      });

      const subjectId = subjectResponse.data.id;

      // Save the curriculum
      await axios.post('http://localhost:3001/api/curricula', {
        subjectId: subjectId,
        structure: generatedCurriculum
      });

      onSubjectCreated({
        id: subjectId,
        title: subjectTitle.trim(),
        description: subjectDescription.trim(),
        has_curriculum: true
      });
    } catch (error) {
      console.error('Error creating subject:', error);
      setError('Failed to create subject. Please try again.');
    }
  };

  const handleCurriculumEdit = (updatedCurriculum) => {
    setGeneratedCurriculum(updatedCurriculum);
  };

  if (step === 1) {
    return (
      <motion.div
        className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            className="flex items-center gap-4 mb-8"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Button
              onClick={onBack}
              variant="ghost"
              size="md"
              icon={<ArrowLeft className="w-4 h-4" />}
              className="hover:bg-dark-800"
            >
              Back to Dashboard
            </Button>
            <div className="h-6 w-px bg-dark-700" />
            <div>
              <h1 className="text-3xl font-bold text-dark-50">Create New Learning Subject</h1>
              <p className="text-dark-300 mt-1">Step 1 of 3: Define your learning goals</p>
            </div>
          </motion.div>

          {/* Progress Indicator */}
          <motion.div
            className="flex items-center justify-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  1
                </div>
                <span className="ml-2 text-sm font-medium text-primary-400">Subject Details</span>
              </div>
              <div className="w-12 h-px bg-dark-700" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-dark-700 rounded-full flex items-center justify-center text-dark-400 font-semibold text-sm">
                  2
                </div>
                <span className="ml-2 text-sm text-dark-400">Review Curriculum</span>
              </div>
              <div className="w-12 h-px bg-dark-700" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-dark-700 rounded-full flex items-center justify-center text-dark-400 font-semibold text-sm">
                  3
                </div>
                <span className="ml-2 text-sm text-dark-400">Finalize</span>
              </div>
            </div>
          </motion.div>

          {/* Main Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <Card variant="elevated" className="max-w-2xl mx-auto">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <CardTitle>What would you like to learn?</CardTitle>
                    <p className="text-sm text-dark-300 mt-1">Tell us about your learning goals and we'll create a personalized curriculum</p>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <Input
                  id="subjectTitle"
                  label="Subject Title"
                  value={subjectTitle}
                  onChange={(e) => setSubjectTitle(e.target.value)}
                  placeholder="e.g., React.js, Machine Learning, Photography..."
                  maxLength={100}
                  icon={<Sparkles className="w-4 h-4" />}
                />

                <div className="space-y-2">
                  <label htmlFor="subjectDescription" className="block text-sm font-medium text-dark-200">
                    Description (optional)
                  </label>
                  <textarea
                    id="subjectDescription"
                    value={subjectDescription}
                    onChange={(e) => setSubjectDescription(e.target.value)}
                    className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg text-dark-100 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 resize-none"
                    placeholder="Add any specific details about what you want to focus on..."
                    maxLength={500}
                    rows={3}
                  />
                  <p className="text-xs text-dark-400">{subjectDescription.length}/500 characters</p>
                </div>

                {error && (
                  <motion.div
                    className="bg-error-500/10 border border-error-500 rounded-lg p-4 flex items-start gap-3"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <AlertCircle className="w-5 h-5 text-error-400 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-error-400">{error}</p>
                  </motion.div>
                )}

                <div className="pt-4">
                  <Button
                    onClick={handleGenerateCurriculum}
                    disabled={isGenerating || !subjectTitle.trim()}
                    loading={isGenerating}
                    variant="primary"
                    size="lg"
                    className="w-full shadow-glow"
                    icon={!isGenerating ? <Sparkles className="w-5 h-5" /> : undefined}
                  >
                    {isGenerating ? 'Generating Your Curriculum...' : 'Generate Curriculum'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>
    );
  }

  if (step === 2) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button onClick={() => setStep(1)} className={styles.backButton}>
            ← Back to Subject Details
          </button>
          <h1>Review Your Curriculum</h1>
        </div>

        <div className={styles.curriculumPreview}>
          <div className={styles.subjectInfo}>
            <h2>{subjectTitle}</h2>
            {subjectDescription && <p>{subjectDescription}</p>}
          </div>

          {generatedCurriculum && (
            <div className={styles.curriculum}>
              {generatedCurriculum.curriculum.map((category, categoryIndex) => (
                <div key={category.id} className={styles.category}>
                  <h3 className={styles.categoryTitle}>{category.title}</h3>
                  {category.description && (
                    <p className={styles.categoryDescription}>{category.description}</p>
                  )}
                  <ul className={styles.topicList}>
                    {category.topics.map((topic, topicIndex) => (
                      <li key={topic.id} className={styles.topic}>
                        <span className={styles.topicTitle}>{topic.title}</span>
                        {topic.description && (
                          <span className={styles.topicDescription}>{topic.description}</span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          <div className={styles.actions}>
            <button
              onClick={() => setStep(1)}
              className={styles.editButton}
            >
              Regenerate Curriculum
            </button>
            <button
              onClick={() => setStep(3)}
              className={styles.editButton}
            >
              Edit Curriculum
            </button>
            <button
              onClick={handleCreateSubject}
              className={styles.createButton}
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button onClick={() => setStep(2)} className={styles.backButton}>
            ← Back to Review
          </button>
          <h1>Edit Curriculum</h1>
        </div>

        <div className={styles.editorContainer}>
          <div className={styles.subjectInfo}>
            <h2>{subjectTitle}</h2>
            {subjectDescription && <p>{subjectDescription}</p>}
          </div>

          <CurriculumEditor
            curriculum={generatedCurriculum}
            onCurriculumChange={handleCurriculumEdit}
          />

          <div className={styles.actions}>
            <button
              onClick={() => setStep(2)}
              className={styles.editButton}
            >
              Back to Review
            </button>
            <button
              onClick={handleCreateSubject}
              className={styles.createButton}
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

export default SubjectCreation;
