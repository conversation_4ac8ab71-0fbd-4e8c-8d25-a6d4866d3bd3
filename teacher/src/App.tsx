import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Dashboard from './components/Dashboard';
import SubjectCreation from './components/SubjectCreation';
import SubjectLearning from './components/SubjectLearning';
import Header from './components/Header';
import SettingsModal from './components/SettingsModal';

function App() {
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'create-subject', 'learning'
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [apiKey, setApiKey] = useState('');
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // Load API key from localStorage on component mount
    const savedApiKey = localStorage.getItem('qtmaster_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  const handleSubjectSelect = (subject) => {
    setSelectedSubject(subject);
    setCurrentView('learning');
  };

  const handleCreateSubject = () => {
    setCurrentView('create-subject');
  };

  const handleSubjectCreated = (newSubject) => {
    setSelectedSubject(newSubject);
    setCurrentView('learning');
  };

  const handleBackToDashboard = () => {
    setSelectedSubject(null);
    setCurrentView('dashboard');
  };

  const handleSaveApiKey = (key) => {
    setApiKey(key);
    localStorage.setItem('qtmaster_api_key', key);
    setShowSettings(false);
  };

  const renderCurrentView = () => {
    const pageVariants = {
      initial: { opacity: 0, x: 20 },
      in: { opacity: 1, x: 0 },
      out: { opacity: 0, x: -20 }
    };

    const pageTransition = {
      type: 'tween',
      ease: 'anticipate',
      duration: 0.4
    };

    switch (currentView) {
      case 'dashboard':
        return (
          <motion.div
            key="dashboard"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <Dashboard
              onSubjectSelect={handleSubjectSelect}
              onCreateSubject={handleCreateSubject}
            />
          </motion.div>
        );
      case 'create-subject':
        return (
          <motion.div
            key="create-subject"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <SubjectCreation
              onBack={handleBackToDashboard}
              onSubjectCreated={handleSubjectCreated}
              apiKey={apiKey}
            />
          </motion.div>
        );
      case 'learning':
        return (
          <motion.div
            key="learning"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <SubjectLearning
              subject={selectedSubject}
              onBack={handleBackToDashboard}
              apiKey={apiKey}
            />
          </motion.div>
        );
      default:
        return (
          <motion.div
            key="default"
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
          >
            <Dashboard onSubjectSelect={handleSubjectSelect} onCreateSubject={handleCreateSubject} />
          </motion.div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950">
      {currentView === 'dashboard' && (
        <Header onSettingsClick={() => setShowSettings(true)} />
      )}

      <AnimatePresence mode="wait">
        {renderCurrentView()}
      </AnimatePresence>

      {showSettings && (
        <SettingsModal
          onClose={() => setShowSettings(false)}
          onSave={handleSaveApiKey}
          initialApiKey={apiKey}
        />
      )}
    </div>
  );
}

export default App;
