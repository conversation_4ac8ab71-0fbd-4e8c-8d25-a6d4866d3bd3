(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))u(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&u(d)}).observe(document,{childList:!0,subtree:!0});function r(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function u(s){if(s.ep)return;s.ep=!0;const f=r(s);fetch(s.href,f)}})();function Rm(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var Rs={exports:{}},ga={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ep;function F0(){if(Ep)return ga;Ep=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function r(u,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var h in s)h!=="key"&&(f[h]=s[h])}else f=s;return s=f.ref,{$$typeof:l,type:u,key:d,ref:s!==void 0?s:null,props:f}}return ga.Fragment=a,ga.jsx=r,ga.jsxs=r,ga}var Tp;function P0(){return Tp||(Tp=1,Rs.exports=F0()),Rs.exports}var z=P0(),zs={exports:{}},be={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ap;function I0(){if(Ap)return be;Ap=1;var l=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),S=Symbol.iterator;function T(g){return g===null||typeof g!="object"?null:(g=S&&g[S]||g["@@iterator"],typeof g=="function"?g:null)}var v={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_=Object.assign,U={};function C(g,G,I){this.props=g,this.context=G,this.refs=U,this.updater=I||v}C.prototype.isReactComponent={},C.prototype.setState=function(g,G){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,G,"setState")},C.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function N(){}N.prototype=C.prototype;function j(g,G,I){this.props=g,this.context=G,this.refs=U,this.updater=I||v}var H=j.prototype=new N;H.constructor=j,_(H,C.prototype),H.isPureReactComponent=!0;var P=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},q=Object.prototype.hasOwnProperty;function oe(g,G,I,E,te,Se){return I=Se.ref,{$$typeof:l,type:g,key:G,ref:I!==void 0?I:null,props:Se}}function ye(g,G){return oe(g.type,G,void 0,void 0,void 0,g.props)}function me(g){return typeof g=="object"&&g!==null&&g.$$typeof===l}function Oe(g){var G={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(I){return G[I]})}var ie=/\/+/g;function $(g,G){return typeof g=="object"&&g!==null&&g.key!=null?Oe(""+g.key):G.toString(36)}function re(){}function W(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(re,re):(g.status="pending",g.then(function(G){g.status==="pending"&&(g.status="fulfilled",g.value=G)},function(G){g.status==="pending"&&(g.status="rejected",g.reason=G)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function ne(g,G,I,E,te){var Se=typeof g;(Se==="undefined"||Se==="boolean")&&(g=null);var le=!1;if(g===null)le=!0;else switch(Se){case"bigint":case"string":case"number":le=!0;break;case"object":switch(g.$$typeof){case l:case a:le=!0;break;case m:return le=g._init,ne(le(g._payload),G,I,E,te)}}if(le)return te=te(g),le=E===""?"."+$(g,0):E,P(te)?(I="",le!=null&&(I=le.replace(ie,"$&/")+"/"),ne(te,G,I,"",function(mt){return mt})):te!=null&&(me(te)&&(te=ye(te,I+(te.key==null||g&&g.key===te.key?"":(""+te.key).replace(ie,"$&/")+"/")+le)),G.push(te)),1;le=0;var We=E===""?".":E+":";if(P(g))for(var Ce=0;Ce<g.length;Ce++)E=g[Ce],Se=We+$(E,Ce),le+=ne(E,G,I,Se,te);else if(Ce=T(g),typeof Ce=="function")for(g=Ce.call(g),Ce=0;!(E=g.next()).done;)E=E.value,Se=We+$(E,Ce++),le+=ne(E,G,I,Se,te);else if(Se==="object"){if(typeof g.then=="function")return ne(W(g),G,I,E,te);throw G=String(g),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return le}function B(g,G,I){if(g==null)return g;var E=[],te=0;return ne(g,E,"","",function(Se){return G.call(I,Se,te++)}),E}function J(g){if(g._status===-1){var G=g._result;G=G(),G.then(function(I){(g._status===0||g._status===-1)&&(g._status=1,g._result=I)},function(I){(g._status===0||g._status===-1)&&(g._status=2,g._result=I)}),g._status===-1&&(g._status=0,g._result=G)}if(g._status===1)return g._result.default;throw g._result}var ae=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function Te(){}return be.Children={map:B,forEach:function(g,G,I){B(g,function(){G.apply(this,arguments)},I)},count:function(g){var G=0;return B(g,function(){G++}),G},toArray:function(g){return B(g,function(G){return G})||[]},only:function(g){if(!me(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},be.Component=C,be.Fragment=r,be.Profiler=s,be.PureComponent=j,be.StrictMode=u,be.Suspense=b,be.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,be.__COMPILER_RUNTIME={__proto__:null,c:function(g){return F.H.useMemoCache(g)}},be.cache=function(g){return function(){return g.apply(null,arguments)}},be.cloneElement=function(g,G,I){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var E=_({},g.props),te=g.key,Se=void 0;if(G!=null)for(le in G.ref!==void 0&&(Se=void 0),G.key!==void 0&&(te=""+G.key),G)!q.call(G,le)||le==="key"||le==="__self"||le==="__source"||le==="ref"&&G.ref===void 0||(E[le]=G[le]);var le=arguments.length-2;if(le===1)E.children=I;else if(1<le){for(var We=Array(le),Ce=0;Ce<le;Ce++)We[Ce]=arguments[Ce+2];E.children=We}return oe(g.type,te,void 0,void 0,Se,E)},be.createContext=function(g){return g={$$typeof:d,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:f,_context:g},g},be.createElement=function(g,G,I){var E,te={},Se=null;if(G!=null)for(E in G.key!==void 0&&(Se=""+G.key),G)q.call(G,E)&&E!=="key"&&E!=="__self"&&E!=="__source"&&(te[E]=G[E]);var le=arguments.length-2;if(le===1)te.children=I;else if(1<le){for(var We=Array(le),Ce=0;Ce<le;Ce++)We[Ce]=arguments[Ce+2];te.children=We}if(g&&g.defaultProps)for(E in le=g.defaultProps,le)te[E]===void 0&&(te[E]=le[E]);return oe(g,Se,void 0,void 0,null,te)},be.createRef=function(){return{current:null}},be.forwardRef=function(g){return{$$typeof:h,render:g}},be.isValidElement=me,be.lazy=function(g){return{$$typeof:m,_payload:{_status:-1,_result:g},_init:J}},be.memo=function(g,G){return{$$typeof:p,type:g,compare:G===void 0?null:G}},be.startTransition=function(g){var G=F.T,I={};F.T=I;try{var E=g(),te=F.S;te!==null&&te(I,E),typeof E=="object"&&E!==null&&typeof E.then=="function"&&E.then(Te,ae)}catch(Se){ae(Se)}finally{F.T=G}},be.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},be.use=function(g){return F.H.use(g)},be.useActionState=function(g,G,I){return F.H.useActionState(g,G,I)},be.useCallback=function(g,G){return F.H.useCallback(g,G)},be.useContext=function(g){return F.H.useContext(g)},be.useDebugValue=function(){},be.useDeferredValue=function(g,G){return F.H.useDeferredValue(g,G)},be.useEffect=function(g,G,I){var E=F.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return E.useEffect(g,G)},be.useId=function(){return F.H.useId()},be.useImperativeHandle=function(g,G,I){return F.H.useImperativeHandle(g,G,I)},be.useInsertionEffect=function(g,G){return F.H.useInsertionEffect(g,G)},be.useLayoutEffect=function(g,G){return F.H.useLayoutEffect(g,G)},be.useMemo=function(g,G){return F.H.useMemo(g,G)},be.useOptimistic=function(g,G){return F.H.useOptimistic(g,G)},be.useReducer=function(g,G,I){return F.H.useReducer(g,G,I)},be.useRef=function(g){return F.H.useRef(g)},be.useState=function(g){return F.H.useState(g)},be.useSyncExternalStore=function(g,G,I){return F.H.useSyncExternalStore(g,G,I)},be.useTransition=function(){return F.H.useTransition()},be.version="19.1.1",be}var wp;function pc(){return wp||(wp=1,zs.exports=I0()),zs.exports}var Ve=pc(),Ds={exports:{}},ba={},ks={exports:{}},Ms={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Op;function $0(){return Op||(Op=1,(function(l){function a(B,J){var ae=B.length;B.push(J);e:for(;0<ae;){var Te=ae-1>>>1,g=B[Te];if(0<s(g,J))B[Te]=J,B[ae]=g,ae=Te;else break e}}function r(B){return B.length===0?null:B[0]}function u(B){if(B.length===0)return null;var J=B[0],ae=B.pop();if(ae!==J){B[0]=ae;e:for(var Te=0,g=B.length,G=g>>>1;Te<G;){var I=2*(Te+1)-1,E=B[I],te=I+1,Se=B[te];if(0>s(E,ae))te<g&&0>s(Se,E)?(B[Te]=Se,B[te]=ae,Te=te):(B[Te]=E,B[I]=ae,Te=I);else if(te<g&&0>s(Se,ae))B[Te]=Se,B[te]=ae,Te=te;else break e}}return J}function s(B,J){var ae=B.sortIndex-J.sortIndex;return ae!==0?ae:B.id-J.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();l.unstable_now=function(){return d.now()-h}}var b=[],p=[],m=1,S=null,T=3,v=!1,_=!1,U=!1,C=!1,N=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,H=typeof setImmediate<"u"?setImmediate:null;function P(B){for(var J=r(p);J!==null;){if(J.callback===null)u(p);else if(J.startTime<=B)u(p),J.sortIndex=J.expirationTime,a(b,J);else break;J=r(p)}}function F(B){if(U=!1,P(B),!_)if(r(b)!==null)_=!0,q||(q=!0,$());else{var J=r(p);J!==null&&ne(F,J.startTime-B)}}var q=!1,oe=-1,ye=5,me=-1;function Oe(){return C?!0:!(l.unstable_now()-me<ye)}function ie(){if(C=!1,q){var B=l.unstable_now();me=B;var J=!0;try{e:{_=!1,U&&(U=!1,j(oe),oe=-1),v=!0;var ae=T;try{t:{for(P(B),S=r(b);S!==null&&!(S.expirationTime>B&&Oe());){var Te=S.callback;if(typeof Te=="function"){S.callback=null,T=S.priorityLevel;var g=Te(S.expirationTime<=B);if(B=l.unstable_now(),typeof g=="function"){S.callback=g,P(B),J=!0;break t}S===r(b)&&u(b),P(B)}else u(b);S=r(b)}if(S!==null)J=!0;else{var G=r(p);G!==null&&ne(F,G.startTime-B),J=!1}}break e}finally{S=null,T=ae,v=!1}J=void 0}}finally{J?$():q=!1}}}var $;if(typeof H=="function")$=function(){H(ie)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,W=re.port2;re.port1.onmessage=ie,$=function(){W.postMessage(null)}}else $=function(){N(ie,0)};function ne(B,J){oe=N(function(){B(l.unstable_now())},J)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(B){B.callback=null},l.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ye=0<B?Math.floor(1e3/B):5},l.unstable_getCurrentPriorityLevel=function(){return T},l.unstable_next=function(B){switch(T){case 1:case 2:case 3:var J=3;break;default:J=T}var ae=T;T=J;try{return B()}finally{T=ae}},l.unstable_requestPaint=function(){C=!0},l.unstable_runWithPriority=function(B,J){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var ae=T;T=B;try{return J()}finally{T=ae}},l.unstable_scheduleCallback=function(B,J,ae){var Te=l.unstable_now();switch(typeof ae=="object"&&ae!==null?(ae=ae.delay,ae=typeof ae=="number"&&0<ae?Te+ae:Te):ae=Te,B){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=ae+g,B={id:m++,callback:J,priorityLevel:B,startTime:ae,expirationTime:g,sortIndex:-1},ae>Te?(B.sortIndex=ae,a(p,B),r(b)===null&&B===r(p)&&(U?(j(oe),oe=-1):U=!0,ne(F,ae-Te))):(B.sortIndex=g,a(b,B),_||v||(_=!0,q||(q=!0,$()))),B},l.unstable_shouldYield=Oe,l.unstable_wrapCallback=function(B){var J=T;return function(){var ae=T;T=J;try{return B.apply(this,arguments)}finally{T=ae}}}})(Ms)),Ms}var _p;function W0(){return _p||(_p=1,ks.exports=$0()),ks.exports}var js={exports:{}},dt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cp;function eb(){if(Cp)return dt;Cp=1;var l=pc();function a(b){var p="https://react.dev/errors/"+b;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var m=2;m<arguments.length;m++)p+="&args[]="+encodeURIComponent(arguments[m])}return"Minified React error #"+b+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var u={d:{f:r,r:function(){throw Error(a(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(b,p,m){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:S==null?null:""+S,children:b,containerInfo:p,implementation:m}}var d=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(b,p){if(b==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return dt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,dt.createPortal=function(b,p){var m=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(a(299));return f(b,p,null,m)},dt.flushSync=function(b){var p=d.T,m=u.p;try{if(d.T=null,u.p=2,b)return b()}finally{d.T=p,u.p=m,u.d.f()}},dt.preconnect=function(b,p){typeof b=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,u.d.C(b,p))},dt.prefetchDNS=function(b){typeof b=="string"&&u.d.D(b)},dt.preinit=function(b,p){if(typeof b=="string"&&p&&typeof p.as=="string"){var m=p.as,S=h(m,p.crossOrigin),T=typeof p.integrity=="string"?p.integrity:void 0,v=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;m==="style"?u.d.S(b,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:S,integrity:T,fetchPriority:v}):m==="script"&&u.d.X(b,{crossOrigin:S,integrity:T,fetchPriority:v,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},dt.preinitModule=function(b,p){if(typeof b=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var m=h(p.as,p.crossOrigin);u.d.M(b,{crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&u.d.M(b)},dt.preload=function(b,p){if(typeof b=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var m=p.as,S=h(m,p.crossOrigin);u.d.L(b,m,{crossOrigin:S,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},dt.preloadModule=function(b,p){if(typeof b=="string")if(p){var m=h(p.as,p.crossOrigin);u.d.m(b,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else u.d.m(b)},dt.requestFormReset=function(b){u.d.r(b)},dt.unstable_batchedUpdates=function(b,p){return b(p)},dt.useFormState=function(b,p,m){return d.H.useFormState(b,p,m)},dt.useFormStatus=function(){return d.H.useHostTransitionStatus()},dt.version="19.1.1",dt}var Np;function tb(){if(Np)return js.exports;Np=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),js.exports=eb(),js.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rp;function nb(){if(Rp)return ba;Rp=1;var l=W0(),a=pc(),r=tb();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(u(188))}function b(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(u(188));return t!==e?null:e}for(var n=e,i=t;;){var o=n.return;if(o===null)break;var c=o.alternate;if(c===null){if(i=o.return,i!==null){n=i;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===n)return h(o),e;if(c===i)return h(o),t;c=c.sibling}throw Error(u(188))}if(n.return!==i.return)n=o,i=c;else{for(var y=!1,x=o.child;x;){if(x===n){y=!0,n=o,i=c;break}if(x===i){y=!0,i=o,n=c;break}x=x.sibling}if(!y){for(x=c.child;x;){if(x===n){y=!0,n=c,i=o;break}if(x===i){y=!0,i=c,n=o;break}x=x.sibling}if(!y)throw Error(u(189))}}if(n.alternate!==i)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var m=Object.assign,S=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),v=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),U=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),j=Symbol.for("react.consumer"),H=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),q=Symbol.for("react.suspense_list"),oe=Symbol.for("react.memo"),ye=Symbol.for("react.lazy"),me=Symbol.for("react.activity"),Oe=Symbol.for("react.memo_cache_sentinel"),ie=Symbol.iterator;function $(e){return e===null||typeof e!="object"?null:(e=ie&&e[ie]||e["@@iterator"],typeof e=="function"?e:null)}var re=Symbol.for("react.client.reference");function W(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===re?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _:return"Fragment";case C:return"Profiler";case U:return"StrictMode";case F:return"Suspense";case q:return"SuspenseList";case me:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case v:return"Portal";case H:return(e.displayName||"Context")+".Provider";case j:return(e._context.displayName||"Context")+".Consumer";case P:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oe:return t=e.displayName||null,t!==null?t:W(e.type)||"Memo";case ye:t=e._payload,e=e._init;try{return W(e(t))}catch{}}return null}var ne=Array.isArray,B=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ae={pending:!1,data:null,method:null,action:null},Te=[],g=-1;function G(e){return{current:e}}function I(e){0>g||(e.current=Te[g],Te[g]=null,g--)}function E(e,t){g++,Te[g]=e.current,e.current=t}var te=G(null),Se=G(null),le=G(null),We=G(null);function Ce(e,t){switch(E(le,t),E(Se,e),E(te,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Ph(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Ph(t),e=Ih(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(te),E(te,e)}function mt(){I(te),I(Se),I(le)}function nn(e){e.memoizedState!==null&&E(We,e);var t=te.current,n=Ih(t,e.type);t!==n&&(E(Se,e),E(te,n))}function pn(e){Se.current===e&&(I(te),I(Se)),We.current===e&&(I(We),da._currentValue=ae)}var gi=Object.prototype.hasOwnProperty,bi=l.unstable_scheduleCallback,xi=l.unstable_cancelCallback,ja=l.unstable_shouldYield,Ua=l.unstable_requestPaint,Ct=l.unstable_now,pu=l.unstable_getCurrentPriorityLevel,Si=l.unstable_ImmediatePriority,vi=l.unstable_UserBlockingPriority,Tl=l.unstable_NormalPriority,mu=l.unstable_LowPriority,Ba=l.unstable_IdlePriority,yu=l.log,gu=l.unstable_setDisableYieldValue,Y=null,Z=null;function se(e){if(typeof yu=="function"&&gu(e),Z&&typeof Z.setStrictMode=="function")try{Z.setStrictMode(Y,e)}catch{}}var de=Math.clz32?Math.clz32:mn,De=Math.log,Nt=Math.LN2;function mn(e){return e>>>=0,e===0?32:31-(De(e)/Nt|0)|0}var gt=256,ln=4194304;function Rt(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function nt(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var o=0,c=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var x=i&134217727;return x!==0?(i=x&~c,i!==0?o=Rt(i):(y&=x,y!==0?o=Rt(y):n||(n=x&~e,n!==0&&(o=Rt(n))))):(x=i&~c,x!==0?o=Rt(x):y!==0?o=Rt(y):n||(n=i&~e,n!==0&&(o=Rt(n)))),o===0?0:t!==0&&t!==o&&(t&c)===0&&(c=o&-o,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:o}function qt(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function It(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Dc(){var e=gt;return gt<<=1,(gt&4194048)===0&&(gt=256),e}function kc(){var e=ln;return ln<<=1,(ln&62914560)===0&&(ln=4194304),e}function bu(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ei(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function By(e,t,n,i,o,c){var y=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var x=e.entanglements,A=e.expirationTimes,D=e.hiddenUpdates;for(n=y&~n;0<n;){var V=31-de(n),Q=1<<V;x[V]=0,A[V]=-1;var k=D[V];if(k!==null)for(D[V]=null,V=0;V<k.length;V++){var M=k[V];M!==null&&(M.lane&=-536870913)}n&=~Q}i!==0&&Mc(e,i,0),c!==0&&o===0&&e.tag!==0&&(e.suspendedLanes|=c&~(y&~t))}function Mc(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-de(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function jc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-de(n),o=1<<i;o&t|e[i]&t&&(e[i]|=t),n&=~o}}function xu(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Su(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Uc(){var e=J.p;return e!==0?e:(e=window.event,e===void 0?32:yp(e.type))}function Ly(e,t){var n=J.p;try{return J.p=e,t()}finally{J.p=n}}var zn=Math.random().toString(36).slice(2),ct="__reactFiber$"+zn,bt="__reactProps$"+zn,Al="__reactContainer$"+zn,vu="__reactEvents$"+zn,Hy="__reactListeners$"+zn,qy="__reactHandles$"+zn,Bc="__reactResources$"+zn,Ti="__reactMarker$"+zn;function Eu(e){delete e[ct],delete e[bt],delete e[vu],delete e[Hy],delete e[qy]}function wl(e){var t=e[ct];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Al]||n[ct]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=tp(e);e!==null;){if(n=e[ct])return n;e=tp(e)}return t}e=n,n=e.parentNode}return null}function Ol(e){if(e=e[ct]||e[Al]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ai(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function _l(e){var t=e[Bc];return t||(t=e[Bc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function lt(e){e[Ti]=!0}var Lc=new Set,Hc={};function nl(e,t){Cl(e,t),Cl(e+"Capture",t)}function Cl(e,t){for(Hc[e]=t,e=0;e<t.length;e++)Lc.add(t[e])}var Yy=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),qc={},Yc={};function Vy(e){return gi.call(Yc,e)?!0:gi.call(qc,e)?!1:Yy.test(e)?Yc[e]=!0:(qc[e]=!0,!1)}function La(e,t,n){if(Vy(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Ha(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function yn(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var Tu,Vc;function Nl(e){if(Tu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Tu=t&&t[1]||"",Vc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Tu+e+Vc}var Au=!1;function wu(e,t){if(!e||Au)return"";Au=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(M){var k=M}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(M){k=M}e.call(Q.prototype)}}else{try{throw Error()}catch(M){k=M}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(M){if(M&&k&&typeof M.stack=="string")return[M.stack,k.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),y=c[0],x=c[1];if(y&&x){var A=y.split(`
`),D=x.split(`
`);for(o=i=0;i<A.length&&!A[i].includes("DetermineComponentFrameRoot");)i++;for(;o<D.length&&!D[o].includes("DetermineComponentFrameRoot");)o++;if(i===A.length||o===D.length)for(i=A.length-1,o=D.length-1;1<=i&&0<=o&&A[i]!==D[o];)o--;for(;1<=i&&0<=o;i--,o--)if(A[i]!==D[o]){if(i!==1||o!==1)do if(i--,o--,0>o||A[i]!==D[o]){var V=`
`+A[i].replace(" at new "," at ");return e.displayName&&V.includes("<anonymous>")&&(V=V.replace("<anonymous>",e.displayName)),V}while(1<=i&&0<=o);break}}}finally{Au=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Nl(n):""}function Xy(e){switch(e.tag){case 26:case 27:case 5:return Nl(e.type);case 16:return Nl("Lazy");case 13:return Nl("Suspense");case 19:return Nl("SuspenseList");case 0:case 15:return wu(e.type,!1);case 11:return wu(e.type.render,!1);case 1:return wu(e.type,!0);case 31:return Nl("Activity");default:return""}}function Xc(e){try{var t="";do t+=Xy(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Yt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Gc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Gy(e){var t=Gc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(y){i=""+y,c.call(this,y)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(y){i=""+y},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function qa(e){e._valueTracker||(e._valueTracker=Gy(e))}function Qc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=Gc(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function Ya(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Qy=/[\n"\\]/g;function Vt(e){return e.replace(Qy,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ou(e,t,n,i,o,c,y,x){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),t!=null?y==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Yt(t)):e.value!==""+Yt(t)&&(e.value=""+Yt(t)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),t!=null?_u(e,y,Yt(t)):n!=null?_u(e,y,Yt(n)):i!=null&&e.removeAttribute("value"),o==null&&c!=null&&(e.defaultChecked=!!c),o!=null&&(e.checked=o&&typeof o!="function"&&typeof o!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?e.name=""+Yt(x):e.removeAttribute("name")}function Zc(e,t,n,i,o,c,y,x){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Yt(n):"",t=t!=null?""+Yt(t):n,x||t===e.value||(e.value=t),e.defaultValue=t}i=i??o,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=x?e.checked:!!i,e.defaultChecked=!!i,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function _u(e,t,n){t==="number"&&Ya(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Rl(e,t,n,i){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&i&&(e[n].defaultSelected=!0)}else{for(n=""+Yt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,i&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Kc(e,t,n){if(t!=null&&(t=""+Yt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Yt(n):""}function Jc(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(u(92));if(ne(i)){if(1<i.length)throw Error(u(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=Yt(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function zl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zy=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Fc(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Zy.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Pc(e,t,n){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var o in t)i=t[o],t.hasOwnProperty(o)&&n[o]!==i&&Fc(e,o,i)}else for(var c in t)t.hasOwnProperty(c)&&Fc(e,c,t[c])}function Cu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ky=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Jy=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Va(e){return Jy.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Nu=null;function Ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Dl=null,kl=null;function Ic(e){var t=Ol(e);if(t&&(e=t.stateNode)){var n=e[bt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ou(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Vt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var o=i[bt]||null;if(!o)throw Error(u(90));Ou(i,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&Qc(i)}break e;case"textarea":Kc(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Rl(e,!!n.multiple,t,!1)}}}var zu=!1;function $c(e,t,n){if(zu)return e(t,n);zu=!0;try{var i=e(t);return i}finally{if(zu=!1,(Dl!==null||kl!==null)&&(_r(),Dl&&(t=Dl,e=kl,kl=Dl=null,Ic(t),e)))for(t=0;t<e.length;t++)Ic(e[t])}}function wi(e,t){var n=e.stateNode;if(n===null)return null;var i=n[bt]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(u(231,t,typeof n));return n}var gn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Du=!1;if(gn)try{var Oi={};Object.defineProperty(Oi,"passive",{get:function(){Du=!0}}),window.addEventListener("test",Oi,Oi),window.removeEventListener("test",Oi,Oi)}catch{Du=!1}var Dn=null,ku=null,Xa=null;function Wc(){if(Xa)return Xa;var e,t=ku,n=t.length,i,o="value"in Dn?Dn.value:Dn.textContent,c=o.length;for(e=0;e<n&&t[e]===o[e];e++);var y=n-e;for(i=1;i<=y&&t[n-i]===o[c-i];i++);return Xa=o.slice(e,1<i?1-i:void 0)}function Ga(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Qa(){return!0}function ef(){return!1}function xt(e){function t(n,i,o,c,y){this._reactName=n,this._targetInst=o,this.type=i,this.nativeEvent=c,this.target=y,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(n=e[x],this[x]=n?n(c):c[x]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Qa:ef,this.isPropagationStopped=ef,this}return m(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Qa)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Qa)},persist:function(){},isPersistent:Qa}),t}var ll={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Za=xt(ll),_i=m({},ll,{view:0,detail:0}),Fy=xt(_i),Mu,ju,Ci,Ka=m({},_i,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ci&&(Ci&&e.type==="mousemove"?(Mu=e.screenX-Ci.screenX,ju=e.screenY-Ci.screenY):ju=Mu=0,Ci=e),Mu)},movementY:function(e){return"movementY"in e?e.movementY:ju}}),tf=xt(Ka),Py=m({},Ka,{dataTransfer:0}),Iy=xt(Py),$y=m({},_i,{relatedTarget:0}),Uu=xt($y),Wy=m({},ll,{animationName:0,elapsedTime:0,pseudoElement:0}),eg=xt(Wy),tg=m({},ll,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ng=xt(tg),lg=m({},ll,{data:0}),nf=xt(lg),ig={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ag={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},rg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ug(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=rg[e])?!!t[e]:!1}function Bu(){return ug}var og=m({},_i,{key:function(e){if(e.key){var t=ig[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ga(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ag[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bu,charCode:function(e){return e.type==="keypress"?Ga(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ga(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),sg=xt(og),cg=m({},Ka,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),lf=xt(cg),fg=m({},_i,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bu}),dg=xt(fg),hg=m({},ll,{propertyName:0,elapsedTime:0,pseudoElement:0}),pg=xt(hg),mg=m({},Ka,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),yg=xt(mg),gg=m({},ll,{newState:0,oldState:0}),bg=xt(gg),xg=[9,13,27,32],Lu=gn&&"CompositionEvent"in window,Ni=null;gn&&"documentMode"in document&&(Ni=document.documentMode);var Sg=gn&&"TextEvent"in window&&!Ni,af=gn&&(!Lu||Ni&&8<Ni&&11>=Ni),rf=" ",uf=!1;function of(e,t){switch(e){case"keyup":return xg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function sf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ml=!1;function vg(e,t){switch(e){case"compositionend":return sf(t);case"keypress":return t.which!==32?null:(uf=!0,rf);case"textInput":return e=t.data,e===rf&&uf?null:e;default:return null}}function Eg(e,t){if(Ml)return e==="compositionend"||!Lu&&of(e,t)?(e=Wc(),Xa=ku=Dn=null,Ml=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return af&&t.locale!=="ko"?null:t.data;default:return null}}var Tg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Tg[e.type]:t==="textarea"}function ff(e,t,n,i){Dl?kl?kl.push(i):kl=[i]:Dl=i,t=kr(t,"onChange"),0<t.length&&(n=new Za("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Ri=null,zi=null;function Ag(e){Qh(e,0)}function Ja(e){var t=Ai(e);if(Qc(t))return e}function df(e,t){if(e==="change")return t}var hf=!1;if(gn){var Hu;if(gn){var qu="oninput"in document;if(!qu){var pf=document.createElement("div");pf.setAttribute("oninput","return;"),qu=typeof pf.oninput=="function"}Hu=qu}else Hu=!1;hf=Hu&&(!document.documentMode||9<document.documentMode)}function mf(){Ri&&(Ri.detachEvent("onpropertychange",yf),zi=Ri=null)}function yf(e){if(e.propertyName==="value"&&Ja(zi)){var t=[];ff(t,zi,e,Ru(e)),$c(Ag,t)}}function wg(e,t,n){e==="focusin"?(mf(),Ri=t,zi=n,Ri.attachEvent("onpropertychange",yf)):e==="focusout"&&mf()}function Og(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ja(zi)}function _g(e,t){if(e==="click")return Ja(t)}function Cg(e,t){if(e==="input"||e==="change")return Ja(t)}function Ng(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var zt=typeof Object.is=="function"?Object.is:Ng;function Di(e,t){if(zt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var o=n[i];if(!gi.call(t,o)||!zt(e[o],t[o]))return!1}return!0}function gf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function bf(e,t){var n=gf(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=gf(n)}}function xf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Sf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ya(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ya(e.document)}return t}function Yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Rg=gn&&"documentMode"in document&&11>=document.documentMode,jl=null,Vu=null,ki=null,Xu=!1;function vf(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xu||jl==null||jl!==Ya(i)||(i=jl,"selectionStart"in i&&Yu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ki&&Di(ki,i)||(ki=i,i=kr(Vu,"onSelect"),0<i.length&&(t=new Za("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=jl)))}function il(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ul={animationend:il("Animation","AnimationEnd"),animationiteration:il("Animation","AnimationIteration"),animationstart:il("Animation","AnimationStart"),transitionrun:il("Transition","TransitionRun"),transitionstart:il("Transition","TransitionStart"),transitioncancel:il("Transition","TransitionCancel"),transitionend:il("Transition","TransitionEnd")},Gu={},Ef={};gn&&(Ef=document.createElement("div").style,"AnimationEvent"in window||(delete Ul.animationend.animation,delete Ul.animationiteration.animation,delete Ul.animationstart.animation),"TransitionEvent"in window||delete Ul.transitionend.transition);function al(e){if(Gu[e])return Gu[e];if(!Ul[e])return e;var t=Ul[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ef)return Gu[e]=t[n];return e}var Tf=al("animationend"),Af=al("animationiteration"),wf=al("animationstart"),zg=al("transitionrun"),Dg=al("transitionstart"),kg=al("transitioncancel"),Of=al("transitionend"),_f=new Map,Qu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Qu.push("scrollEnd");function $t(e,t){_f.set(e,t),nl(t,[e])}var Cf=new WeakMap;function Xt(e,t){if(typeof e=="object"&&e!==null){var n=Cf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Xc(t)},Cf.set(e,t),t)}return{value:e,source:t,stack:Xc(t)}}var Gt=[],Bl=0,Zu=0;function Fa(){for(var e=Bl,t=Zu=Bl=0;t<e;){var n=Gt[t];Gt[t++]=null;var i=Gt[t];Gt[t++]=null;var o=Gt[t];Gt[t++]=null;var c=Gt[t];if(Gt[t++]=null,i!==null&&o!==null){var y=i.pending;y===null?o.next=o:(o.next=y.next,y.next=o),i.pending=o}c!==0&&Nf(n,o,c)}}function Pa(e,t,n,i){Gt[Bl++]=e,Gt[Bl++]=t,Gt[Bl++]=n,Gt[Bl++]=i,Zu|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function Ku(e,t,n,i){return Pa(e,t,n,i),Ia(e)}function Ll(e,t){return Pa(e,null,null,t),Ia(e)}function Nf(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var o=!1,c=e.return;c!==null;)c.childLanes|=n,i=c.alternate,i!==null&&(i.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(o=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,o&&t!==null&&(o=31-de(n),e=c.hiddenUpdates,i=e[o],i===null?e[o]=[t]:i.push(t),t.lane=n|536870912),c):null}function Ia(e){if(50<ia)throw ia=0,es=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Hl={};function Mg(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dt(e,t,n,i){return new Mg(e,t,n,i)}function Ju(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bn(e,t){var n=e.alternate;return n===null?(n=Dt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Rf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function $a(e,t,n,i,o,c){var y=0;if(i=e,typeof e=="function")Ju(e)&&(y=1);else if(typeof e=="string")y=U0(e,n,te.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case me:return e=Dt(31,n,t,o),e.elementType=me,e.lanes=c,e;case _:return rl(n.children,o,c,t);case U:y=8,o|=24;break;case C:return e=Dt(12,n,t,o|2),e.elementType=C,e.lanes=c,e;case F:return e=Dt(13,n,t,o),e.elementType=F,e.lanes=c,e;case q:return e=Dt(19,n,t,o),e.elementType=q,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case N:case H:y=10;break e;case j:y=9;break e;case P:y=11;break e;case oe:y=14;break e;case ye:y=16,i=null;break e}y=29,n=Error(u(130,e===null?"null":typeof e,"")),i=null}return t=Dt(y,n,t,o),t.elementType=e,t.type=i,t.lanes=c,t}function rl(e,t,n,i){return e=Dt(7,e,i,t),e.lanes=n,e}function Fu(e,t,n){return e=Dt(6,e,null,t),e.lanes=n,e}function Pu(e,t,n){return t=Dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ql=[],Yl=0,Wa=null,er=0,Qt=[],Zt=0,ul=null,xn=1,Sn="";function ol(e,t){ql[Yl++]=er,ql[Yl++]=Wa,Wa=e,er=t}function zf(e,t,n){Qt[Zt++]=xn,Qt[Zt++]=Sn,Qt[Zt++]=ul,ul=e;var i=xn;e=Sn;var o=32-de(i)-1;i&=~(1<<o),n+=1;var c=32-de(t)+o;if(30<c){var y=o-o%5;c=(i&(1<<y)-1).toString(32),i>>=y,o-=y,xn=1<<32-de(t)+o|n<<o|i,Sn=c+e}else xn=1<<c|n<<o|i,Sn=e}function Iu(e){e.return!==null&&(ol(e,1),zf(e,1,0))}function $u(e){for(;e===Wa;)Wa=ql[--Yl],ql[Yl]=null,er=ql[--Yl],ql[Yl]=null;for(;e===ul;)ul=Qt[--Zt],Qt[Zt]=null,Sn=Qt[--Zt],Qt[Zt]=null,xn=Qt[--Zt],Qt[Zt]=null}var yt=null,Ze=null,Re=!1,sl=null,an=!1,Wu=Error(u(519));function cl(e){var t=Error(u(418,""));throw Ui(Xt(t,e)),Wu}function Df(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[ct]=e,t[bt]=i,n){case"dialog":we("cancel",t),we("close",t);break;case"iframe":case"object":case"embed":we("load",t);break;case"video":case"audio":for(n=0;n<ra.length;n++)we(ra[n],t);break;case"source":we("error",t);break;case"img":case"image":case"link":we("error",t),we("load",t);break;case"details":we("toggle",t);break;case"input":we("invalid",t),Zc(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),qa(t);break;case"select":we("invalid",t);break;case"textarea":we("invalid",t),Jc(t,i.value,i.defaultValue,i.children),qa(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||Fh(t.textContent,n)?(i.popover!=null&&(we("beforetoggle",t),we("toggle",t)),i.onScroll!=null&&we("scroll",t),i.onScrollEnd!=null&&we("scrollend",t),i.onClick!=null&&(t.onclick=Mr),t=!0):t=!1,t||cl(e)}function kf(e){for(yt=e.return;yt;)switch(yt.tag){case 5:case 13:an=!1;return;case 27:case 3:an=!0;return;default:yt=yt.return}}function Mi(e){if(e!==yt)return!1;if(!Re)return kf(e),Re=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||ys(e.type,e.memoizedProps)),n=!n),n&&Ze&&cl(e),kf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ze=en(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ze=null}}else t===27?(t=Ze,Jn(e.type)?(e=Ss,Ss=null,Ze=e):Ze=t):Ze=yt?en(e.stateNode.nextSibling):null;return!0}function ji(){Ze=yt=null,Re=!1}function Mf(){var e=sl;return e!==null&&(Et===null?Et=e:Et.push.apply(Et,e),sl=null),e}function Ui(e){sl===null?sl=[e]:sl.push(e)}var eo=G(null),fl=null,vn=null;function kn(e,t,n){E(eo,t._currentValue),t._currentValue=n}function En(e){e._currentValue=eo.current,I(eo)}function to(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function no(e,t,n,i){var o=e.child;for(o!==null&&(o.return=e);o!==null;){var c=o.dependencies;if(c!==null){var y=o.child;c=c.firstContext;e:for(;c!==null;){var x=c;c=o;for(var A=0;A<t.length;A++)if(x.context===t[A]){c.lanes|=n,x=c.alternate,x!==null&&(x.lanes|=n),to(c.return,n,e),i||(y=null);break e}c=x.next}}else if(o.tag===18){if(y=o.return,y===null)throw Error(u(341));y.lanes|=n,c=y.alternate,c!==null&&(c.lanes|=n),to(y,n,e),y=null}else y=o.child;if(y!==null)y.return=o;else for(y=o;y!==null;){if(y===e){y=null;break}if(o=y.sibling,o!==null){o.return=y.return,y=o;break}y=y.return}o=y}}function Bi(e,t,n,i){e=null;for(var o=t,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var y=o.alternate;if(y===null)throw Error(u(387));if(y=y.memoizedProps,y!==null){var x=o.type;zt(o.pendingProps.value,y.value)||(e!==null?e.push(x):e=[x])}}else if(o===We.current){if(y=o.alternate,y===null)throw Error(u(387));y.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(e!==null?e.push(da):e=[da])}o=o.return}e!==null&&no(t,e,n,i),t.flags|=262144}function tr(e){for(e=e.firstContext;e!==null;){if(!zt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function dl(e){fl=e,vn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return jf(fl,e)}function nr(e,t){return fl===null&&dl(e),jf(e,t)}function jf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},vn===null){if(e===null)throw Error(u(308));vn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else vn=vn.next=t;return n}var jg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Ug=l.unstable_scheduleCallback,Bg=l.unstable_NormalPriority,et={$$typeof:H,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function lo(){return{controller:new jg,data:new Map,refCount:0}}function Li(e){e.refCount--,e.refCount===0&&Ug(Bg,function(){e.controller.abort()})}var Hi=null,io=0,Vl=0,Xl=null;function Lg(e,t){if(Hi===null){var n=Hi=[];io=0,Vl=us(),Xl={status:"pending",value:void 0,then:function(i){n.push(i)}}}return io++,t.then(Uf,Uf),t}function Uf(){if(--io===0&&Hi!==null){Xl!==null&&(Xl.status="fulfilled");var e=Hi;Hi=null,Vl=0,Xl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Hg(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var o=0;o<n.length;o++)(0,n[o])(t)},function(o){for(i.status="rejected",i.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),i}var Bf=B.S;B.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Lg(e,t),Bf!==null&&Bf(e,t)};var hl=G(null);function ao(){var e=hl.current;return e!==null?e:qe.pooledCache}function lr(e,t){t===null?E(hl,hl.current):E(hl,t.pool)}function Lf(){var e=ao();return e===null?null:{parent:et._currentValue,pool:e}}var qi=Error(u(460)),Hf=Error(u(474)),ir=Error(u(542)),ro={then:function(){}};function qf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ar(){}function Yf(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(ar,ar),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xf(e),e;default:if(typeof t.status=="string")t.then(ar,ar);else{if(e=qe,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var o=t;o.status="fulfilled",o.value=i}},function(i){if(t.status==="pending"){var o=t;o.status="rejected",o.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xf(e),e}throw Yi=t,qi}}var Yi=null;function Vf(){if(Yi===null)throw Error(u(459));var e=Yi;return Yi=null,e}function Xf(e){if(e===qi||e===ir)throw Error(u(483))}var Mn=!1;function uo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function oo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function jn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Un(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(ke&2)!==0){var o=i.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),i.pending=t,t=Ia(e),Nf(e,null,n),t}return Pa(e,i,t,n),Ia(e)}function Vi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,jc(e,n)}}function so(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var o=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var y={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?o=c=y:c=c.next=y,n=n.next}while(n!==null);c===null?o=c=t:c=c.next=t}else o=c=t;n={baseState:i.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var co=!1;function Xi(){if(co){var e=Xl;if(e!==null)throw e}}function Gi(e,t,n,i){co=!1;var o=e.updateQueue;Mn=!1;var c=o.firstBaseUpdate,y=o.lastBaseUpdate,x=o.shared.pending;if(x!==null){o.shared.pending=null;var A=x,D=A.next;A.next=null,y===null?c=D:y.next=D,y=A;var V=e.alternate;V!==null&&(V=V.updateQueue,x=V.lastBaseUpdate,x!==y&&(x===null?V.firstBaseUpdate=D:x.next=D,V.lastBaseUpdate=A))}if(c!==null){var Q=o.baseState;y=0,V=D=A=null,x=c;do{var k=x.lane&-536870913,M=k!==x.lane;if(M?(_e&k)===k:(i&k)===k){k!==0&&k===Vl&&(co=!0),V!==null&&(V=V.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});e:{var he=e,ce=x;k=t;var Be=n;switch(ce.tag){case 1:if(he=ce.payload,typeof he=="function"){Q=he.call(Be,Q,k);break e}Q=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=ce.payload,k=typeof he=="function"?he.call(Be,Q,k):he,k==null)break e;Q=m({},Q,k);break e;case 2:Mn=!0}}k=x.callback,k!==null&&(e.flags|=64,M&&(e.flags|=8192),M=o.callbacks,M===null?o.callbacks=[k]:M.push(k))}else M={lane:k,tag:x.tag,payload:x.payload,callback:x.callback,next:null},V===null?(D=V=M,A=Q):V=V.next=M,y|=k;if(x=x.next,x===null){if(x=o.shared.pending,x===null)break;M=x,x=M.next,M.next=null,o.lastBaseUpdate=M,o.shared.pending=null}}while(!0);V===null&&(A=Q),o.baseState=A,o.firstBaseUpdate=D,o.lastBaseUpdate=V,c===null&&(o.shared.lanes=0),Gn|=y,e.lanes=y,e.memoizedState=Q}}function Gf(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function Qf(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Gf(n[e],t)}var Gl=G(null),rr=G(0);function Zf(e,t){e=Nn,E(rr,e),E(Gl,t),Nn=e|t.baseLanes}function fo(){E(rr,Nn),E(Gl,Gl.current)}function ho(){Nn=rr.current,I(Gl),I(rr)}var Bn=0,ve=null,je=null,Pe=null,ur=!1,Ql=!1,pl=!1,or=0,Qi=0,Zl=null,qg=0;function Je(){throw Error(u(321))}function po(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!zt(e[n],t[n]))return!1;return!0}function mo(e,t,n,i,o,c){return Bn=c,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,B.H=e===null||e.memoizedState===null?Nd:Rd,pl=!1,c=n(i,o),pl=!1,Ql&&(c=Jf(t,n,i,o)),Kf(e),c}function Kf(e){B.H=pr;var t=je!==null&&je.next!==null;if(Bn=0,Pe=je=ve=null,ur=!1,Qi=0,Zl=null,t)throw Error(u(300));e===null||it||(e=e.dependencies,e!==null&&tr(e)&&(it=!0))}function Jf(e,t,n,i){ve=e;var o=0;do{if(Ql&&(Zl=null),Qi=0,Ql=!1,25<=o)throw Error(u(301));if(o+=1,Pe=je=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}B.H=Kg,c=t(n,i)}while(Ql);return c}function Yg(){var e=B.H,t=e.useState()[0];return t=typeof t.then=="function"?Zi(t):t,e=e.useState()[0],(je!==null?je.memoizedState:null)!==e&&(ve.flags|=1024),t}function yo(){var e=or!==0;return or=0,e}function go(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function bo(e){if(ur){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ur=!1}Bn=0,Pe=je=ve=null,Ql=!1,Qi=or=0,Zl=null}function St(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Pe===null?ve.memoizedState=Pe=e:Pe=Pe.next=e,Pe}function Ie(){if(je===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=je.next;var t=Pe===null?ve.memoizedState:Pe.next;if(t!==null)Pe=t,je=e;else{if(e===null)throw ve.alternate===null?Error(u(467)):Error(u(310));je=e,e={memoizedState:je.memoizedState,baseState:je.baseState,baseQueue:je.baseQueue,queue:je.queue,next:null},Pe===null?ve.memoizedState=Pe=e:Pe=Pe.next=e}return Pe}function xo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Zi(e){var t=Qi;return Qi+=1,Zl===null&&(Zl=[]),e=Yf(Zl,e,t),t=ve,(Pe===null?t.memoizedState:Pe.next)===null&&(t=t.alternate,B.H=t===null||t.memoizedState===null?Nd:Rd),e}function sr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Zi(e);if(e.$$typeof===H)return ft(e)}throw Error(u(438,String(e)))}function So(e){var t=null,n=ve.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=ve.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(o){return o.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=xo(),ve.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=Oe;return t.index++,n}function Tn(e,t){return typeof t=="function"?t(e):t}function cr(e){var t=Ie();return vo(t,je,e)}function vo(e,t,n){var i=e.queue;if(i===null)throw Error(u(311));i.lastRenderedReducer=n;var o=e.baseQueue,c=i.pending;if(c!==null){if(o!==null){var y=o.next;o.next=c.next,c.next=y}t.baseQueue=o=c,i.pending=null}if(c=e.baseState,o===null)e.memoizedState=c;else{t=o.next;var x=y=null,A=null,D=t,V=!1;do{var Q=D.lane&-536870913;if(Q!==D.lane?(_e&Q)===Q:(Bn&Q)===Q){var k=D.revertLane;if(k===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),Q===Vl&&(V=!0);else if((Bn&k)===k){D=D.next,k===Vl&&(V=!0);continue}else Q={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},A===null?(x=A=Q,y=c):A=A.next=Q,ve.lanes|=k,Gn|=k;Q=D.action,pl&&n(c,Q),c=D.hasEagerState?D.eagerState:n(c,Q)}else k={lane:Q,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},A===null?(x=A=k,y=c):A=A.next=k,ve.lanes|=Q,Gn|=Q;D=D.next}while(D!==null&&D!==t);if(A===null?y=c:A.next=x,!zt(c,e.memoizedState)&&(it=!0,V&&(n=Xl,n!==null)))throw n;e.memoizedState=c,e.baseState=y,e.baseQueue=A,i.lastRenderedState=c}return o===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Eo(e){var t=Ie(),n=t.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=e;var i=n.dispatch,o=n.pending,c=t.memoizedState;if(o!==null){n.pending=null;var y=o=o.next;do c=e(c,y.action),y=y.next;while(y!==o);zt(c,t.memoizedState)||(it=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,i]}function Ff(e,t,n){var i=ve,o=Ie(),c=Re;if(c){if(n===void 0)throw Error(u(407));n=n()}else n=t();var y=!zt((je||o).memoizedState,n);y&&(o.memoizedState=n,it=!0),o=o.queue;var x=$f.bind(null,i,o,e);if(Ki(2048,8,x,[e]),o.getSnapshot!==t||y||Pe!==null&&Pe.memoizedState.tag&1){if(i.flags|=2048,Kl(9,fr(),If.bind(null,i,o,n,t),null),qe===null)throw Error(u(349));c||(Bn&124)!==0||Pf(i,t,n)}return n}function Pf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ve.updateQueue,t===null?(t=xo(),ve.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function If(e,t,n,i){t.value=n,t.getSnapshot=i,Wf(t)&&ed(e)}function $f(e,t,n){return n(function(){Wf(t)&&ed(e)})}function Wf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!zt(e,n)}catch{return!0}}function ed(e){var t=Ll(e,2);t!==null&&Bt(t,e,2)}function To(e){var t=St();if(typeof e=="function"){var n=e;if(e=n(),pl){se(!0);try{n()}finally{se(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:e},t}function td(e,t,n,i){return e.baseState=n,vo(e,je,typeof i=="function"?i:Tn)}function Vg(e,t,n,i,o){if(hr(e))throw Error(u(485));if(e=t.action,e!==null){var c={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){c.listeners.push(y)}};B.T!==null?n(!0):c.isTransition=!1,i(c),n=t.pending,n===null?(c.next=t.pending=c,nd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function nd(e,t){var n=t.action,i=t.payload,o=e.state;if(t.isTransition){var c=B.T,y={};B.T=y;try{var x=n(o,i),A=B.S;A!==null&&A(y,x),ld(e,t,x)}catch(D){Ao(e,t,D)}finally{B.T=c}}else try{c=n(o,i),ld(e,t,c)}catch(D){Ao(e,t,D)}}function ld(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){id(e,t,i)},function(i){return Ao(e,t,i)}):id(e,t,n)}function id(e,t,n){t.status="fulfilled",t.value=n,ad(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,nd(e,n)))}function Ao(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,ad(t),t=t.next;while(t!==i)}e.action=null}function ad(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function rd(e,t){return t}function ud(e,t){if(Re){var n=qe.formState;if(n!==null){e:{var i=ve;if(Re){if(Ze){t:{for(var o=Ze,c=an;o.nodeType!==8;){if(!c){o=null;break t}if(o=en(o.nextSibling),o===null){o=null;break t}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){Ze=en(o.nextSibling),i=o.data==="F!";break e}}cl(i)}i=!1}i&&(t=n[0])}}return n=St(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rd,lastRenderedState:t},n.queue=i,n=Od.bind(null,ve,i),i.dispatch=n,i=To(!1),c=No.bind(null,ve,!1,i.queue),i=St(),o={state:t,dispatch:null,action:e,pending:null},i.queue=o,n=Vg.bind(null,ve,o,c,n),o.dispatch=n,i.memoizedState=e,[t,n,!1]}function od(e){var t=Ie();return sd(t,je,e)}function sd(e,t,n){if(t=vo(e,t,rd)[0],e=cr(Tn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=Zi(t)}catch(y){throw y===qi?ir:y}else i=t;t=Ie();var o=t.queue,c=o.dispatch;return n!==t.memoizedState&&(ve.flags|=2048,Kl(9,fr(),Xg.bind(null,o,n),null)),[i,c,e]}function Xg(e,t){e.action=t}function cd(e){var t=Ie(),n=je;if(n!==null)return sd(t,n,e);Ie(),t=t.memoizedState,n=Ie();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function Kl(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=ve.updateQueue,t===null&&(t=xo(),ve.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function fr(){return{destroy:void 0,resource:void 0}}function fd(){return Ie().memoizedState}function dr(e,t,n,i){var o=St();i=i===void 0?null:i,ve.flags|=e,o.memoizedState=Kl(1|t,fr(),n,i)}function Ki(e,t,n,i){var o=Ie();i=i===void 0?null:i;var c=o.memoizedState.inst;je!==null&&i!==null&&po(i,je.memoizedState.deps)?o.memoizedState=Kl(t,c,n,i):(ve.flags|=e,o.memoizedState=Kl(1|t,c,n,i))}function dd(e,t){dr(8390656,8,e,t)}function hd(e,t){Ki(2048,8,e,t)}function pd(e,t){return Ki(4,2,e,t)}function md(e,t){return Ki(4,4,e,t)}function yd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function gd(e,t,n){n=n!=null?n.concat([e]):null,Ki(4,4,yd.bind(null,t,e),n)}function wo(){}function bd(e,t){var n=Ie();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&po(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function xd(e,t){var n=Ie();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&po(t,i[1]))return i[0];if(i=e(),pl){se(!0);try{e()}finally{se(!1)}}return n.memoizedState=[i,t],i}function Oo(e,t,n){return n===void 0||(Bn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Eh(),ve.lanes|=e,Gn|=e,n)}function Sd(e,t,n,i){return zt(n,t)?n:Gl.current!==null?(e=Oo(e,n,i),zt(e,t)||(it=!0),e):(Bn&42)===0?(it=!0,e.memoizedState=n):(e=Eh(),ve.lanes|=e,Gn|=e,t)}function vd(e,t,n,i,o){var c=J.p;J.p=c!==0&&8>c?c:8;var y=B.T,x={};B.T=x,No(e,!1,t,n);try{var A=o(),D=B.S;if(D!==null&&D(x,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var V=Hg(A,i);Ji(e,t,V,Ut(e))}else Ji(e,t,i,Ut(e))}catch(Q){Ji(e,t,{then:function(){},status:"rejected",reason:Q},Ut())}finally{J.p=c,B.T=y}}function Gg(){}function _o(e,t,n,i){if(e.tag!==5)throw Error(u(476));var o=Ed(e).queue;vd(e,o,t,ae,n===null?Gg:function(){return Td(e),n(i)})}function Ed(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ae,baseState:ae,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:ae},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Td(e){var t=Ed(e).next.queue;Ji(e,t,{},Ut())}function Co(){return ft(da)}function Ad(){return Ie().memoizedState}function wd(){return Ie().memoizedState}function Qg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Ut();e=jn(n);var i=Un(t,e,n);i!==null&&(Bt(i,t,n),Vi(i,t,n)),t={cache:lo()},e.payload=t;return}t=t.return}}function Zg(e,t,n){var i=Ut();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},hr(e)?_d(t,n):(n=Ku(e,t,n,i),n!==null&&(Bt(n,e,i),Cd(n,t,i)))}function Od(e,t,n){var i=Ut();Ji(e,t,n,i)}function Ji(e,t,n,i){var o={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(hr(e))_d(t,o);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var y=t.lastRenderedState,x=c(y,n);if(o.hasEagerState=!0,o.eagerState=x,zt(x,y))return Pa(e,t,o,0),qe===null&&Fa(),!1}catch{}finally{}if(n=Ku(e,t,o,i),n!==null)return Bt(n,e,i),Cd(n,t,i),!0}return!1}function No(e,t,n,i){if(i={lane:2,revertLane:us(),action:i,hasEagerState:!1,eagerState:null,next:null},hr(e)){if(t)throw Error(u(479))}else t=Ku(e,n,i,2),t!==null&&Bt(t,e,2)}function hr(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function _d(e,t){Ql=ur=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Cd(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,jc(e,n)}}var pr={readContext:ft,use:sr,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useLayoutEffect:Je,useInsertionEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useSyncExternalStore:Je,useId:Je,useHostTransitionStatus:Je,useFormState:Je,useActionState:Je,useOptimistic:Je,useMemoCache:Je,useCacheRefresh:Je},Nd={readContext:ft,use:sr,useCallback:function(e,t){return St().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:dd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,dr(4194308,4,yd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return dr(4194308,4,e,t)},useInsertionEffect:function(e,t){dr(4,2,e,t)},useMemo:function(e,t){var n=St();t=t===void 0?null:t;var i=e();if(pl){se(!0);try{e()}finally{se(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=St();if(n!==void 0){var o=n(t);if(pl){se(!0);try{n(t)}finally{se(!1)}}}else o=t;return i.memoizedState=i.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},i.queue=e,e=e.dispatch=Zg.bind(null,ve,e),[i.memoizedState,e]},useRef:function(e){var t=St();return e={current:e},t.memoizedState=e},useState:function(e){e=To(e);var t=e.queue,n=Od.bind(null,ve,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:wo,useDeferredValue:function(e,t){var n=St();return Oo(n,e,t)},useTransition:function(){var e=To(!1);return e=vd.bind(null,ve,e.queue,!0,!1),St().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=ve,o=St();if(Re){if(n===void 0)throw Error(u(407));n=n()}else{if(n=t(),qe===null)throw Error(u(349));(_e&124)!==0||Pf(i,t,n)}o.memoizedState=n;var c={value:n,getSnapshot:t};return o.queue=c,dd($f.bind(null,i,c,e),[e]),i.flags|=2048,Kl(9,fr(),If.bind(null,i,c,n,t),null),n},useId:function(){var e=St(),t=qe.identifierPrefix;if(Re){var n=Sn,i=xn;n=(i&~(1<<32-de(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=or++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=qg++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Co,useFormState:ud,useActionState:ud,useOptimistic:function(e){var t=St();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=No.bind(null,ve,!0,n),n.dispatch=t,[e,t]},useMemoCache:So,useCacheRefresh:function(){return St().memoizedState=Qg.bind(null,ve)}},Rd={readContext:ft,use:sr,useCallback:bd,useContext:ft,useEffect:hd,useImperativeHandle:gd,useInsertionEffect:pd,useLayoutEffect:md,useMemo:xd,useReducer:cr,useRef:fd,useState:function(){return cr(Tn)},useDebugValue:wo,useDeferredValue:function(e,t){var n=Ie();return Sd(n,je.memoizedState,e,t)},useTransition:function(){var e=cr(Tn)[0],t=Ie().memoizedState;return[typeof e=="boolean"?e:Zi(e),t]},useSyncExternalStore:Ff,useId:Ad,useHostTransitionStatus:Co,useFormState:od,useActionState:od,useOptimistic:function(e,t){var n=Ie();return td(n,je,e,t)},useMemoCache:So,useCacheRefresh:wd},Kg={readContext:ft,use:sr,useCallback:bd,useContext:ft,useEffect:hd,useImperativeHandle:gd,useInsertionEffect:pd,useLayoutEffect:md,useMemo:xd,useReducer:Eo,useRef:fd,useState:function(){return Eo(Tn)},useDebugValue:wo,useDeferredValue:function(e,t){var n=Ie();return je===null?Oo(n,e,t):Sd(n,je.memoizedState,e,t)},useTransition:function(){var e=Eo(Tn)[0],t=Ie().memoizedState;return[typeof e=="boolean"?e:Zi(e),t]},useSyncExternalStore:Ff,useId:Ad,useHostTransitionStatus:Co,useFormState:cd,useActionState:cd,useOptimistic:function(e,t){var n=Ie();return je!==null?td(n,je,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:So,useCacheRefresh:wd},Jl=null,Fi=0;function mr(e){var t=Fi;return Fi+=1,Jl===null&&(Jl=[]),Yf(Jl,e,t)}function Pi(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function yr(e,t){throw t.$$typeof===S?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function zd(e){var t=e._init;return t(e._payload)}function Dd(e){function t(O,w){if(e){var R=O.deletions;R===null?(O.deletions=[w],O.flags|=16):R.push(w)}}function n(O,w){if(!e)return null;for(;w!==null;)t(O,w),w=w.sibling;return null}function i(O){for(var w=new Map;O!==null;)O.key!==null?w.set(O.key,O):w.set(O.index,O),O=O.sibling;return w}function o(O,w){return O=bn(O,w),O.index=0,O.sibling=null,O}function c(O,w,R){return O.index=R,e?(R=O.alternate,R!==null?(R=R.index,R<w?(O.flags|=67108866,w):R):(O.flags|=67108866,w)):(O.flags|=1048576,w)}function y(O){return e&&O.alternate===null&&(O.flags|=67108866),O}function x(O,w,R,X){return w===null||w.tag!==6?(w=Fu(R,O.mode,X),w.return=O,w):(w=o(w,R),w.return=O,w)}function A(O,w,R,X){var ee=R.type;return ee===_?V(O,w,R.props.children,X,R.key):w!==null&&(w.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===ye&&zd(ee)===w.type)?(w=o(w,R.props),Pi(w,R),w.return=O,w):(w=$a(R.type,R.key,R.props,null,O.mode,X),Pi(w,R),w.return=O,w)}function D(O,w,R,X){return w===null||w.tag!==4||w.stateNode.containerInfo!==R.containerInfo||w.stateNode.implementation!==R.implementation?(w=Pu(R,O.mode,X),w.return=O,w):(w=o(w,R.children||[]),w.return=O,w)}function V(O,w,R,X,ee){return w===null||w.tag!==7?(w=rl(R,O.mode,X,ee),w.return=O,w):(w=o(w,R),w.return=O,w)}function Q(O,w,R){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return w=Fu(""+w,O.mode,R),w.return=O,w;if(typeof w=="object"&&w!==null){switch(w.$$typeof){case T:return R=$a(w.type,w.key,w.props,null,O.mode,R),Pi(R,w),R.return=O,R;case v:return w=Pu(w,O.mode,R),w.return=O,w;case ye:var X=w._init;return w=X(w._payload),Q(O,w,R)}if(ne(w)||$(w))return w=rl(w,O.mode,R,null),w.return=O,w;if(typeof w.then=="function")return Q(O,mr(w),R);if(w.$$typeof===H)return Q(O,nr(O,w),R);yr(O,w)}return null}function k(O,w,R,X){var ee=w!==null?w.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return ee!==null?null:x(O,w,""+R,X);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case T:return R.key===ee?A(O,w,R,X):null;case v:return R.key===ee?D(O,w,R,X):null;case ye:return ee=R._init,R=ee(R._payload),k(O,w,R,X)}if(ne(R)||$(R))return ee!==null?null:V(O,w,R,X,null);if(typeof R.then=="function")return k(O,w,mr(R),X);if(R.$$typeof===H)return k(O,w,nr(O,R),X);yr(O,R)}return null}function M(O,w,R,X,ee){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return O=O.get(R)||null,x(w,O,""+X,ee);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case T:return O=O.get(X.key===null?R:X.key)||null,A(w,O,X,ee);case v:return O=O.get(X.key===null?R:X.key)||null,D(w,O,X,ee);case ye:var Ee=X._init;return X=Ee(X._payload),M(O,w,R,X,ee)}if(ne(X)||$(X))return O=O.get(R)||null,V(w,O,X,ee,null);if(typeof X.then=="function")return M(O,w,R,mr(X),ee);if(X.$$typeof===H)return M(O,w,R,nr(w,X),ee);yr(w,X)}return null}function he(O,w,R,X){for(var ee=null,Ee=null,ue=w,fe=w=0,rt=null;ue!==null&&fe<R.length;fe++){ue.index>fe?(rt=ue,ue=null):rt=ue.sibling;var Ne=k(O,ue,R[fe],X);if(Ne===null){ue===null&&(ue=rt);break}e&&ue&&Ne.alternate===null&&t(O,ue),w=c(Ne,w,fe),Ee===null?ee=Ne:Ee.sibling=Ne,Ee=Ne,ue=rt}if(fe===R.length)return n(O,ue),Re&&ol(O,fe),ee;if(ue===null){for(;fe<R.length;fe++)ue=Q(O,R[fe],X),ue!==null&&(w=c(ue,w,fe),Ee===null?ee=ue:Ee.sibling=ue,Ee=ue);return Re&&ol(O,fe),ee}for(ue=i(ue);fe<R.length;fe++)rt=M(ue,O,fe,R[fe],X),rt!==null&&(e&&rt.alternate!==null&&ue.delete(rt.key===null?fe:rt.key),w=c(rt,w,fe),Ee===null?ee=rt:Ee.sibling=rt,Ee=rt);return e&&ue.forEach(function(Wn){return t(O,Wn)}),Re&&ol(O,fe),ee}function ce(O,w,R,X){if(R==null)throw Error(u(151));for(var ee=null,Ee=null,ue=w,fe=w=0,rt=null,Ne=R.next();ue!==null&&!Ne.done;fe++,Ne=R.next()){ue.index>fe?(rt=ue,ue=null):rt=ue.sibling;var Wn=k(O,ue,Ne.value,X);if(Wn===null){ue===null&&(ue=rt);break}e&&ue&&Wn.alternate===null&&t(O,ue),w=c(Wn,w,fe),Ee===null?ee=Wn:Ee.sibling=Wn,Ee=Wn,ue=rt}if(Ne.done)return n(O,ue),Re&&ol(O,fe),ee;if(ue===null){for(;!Ne.done;fe++,Ne=R.next())Ne=Q(O,Ne.value,X),Ne!==null&&(w=c(Ne,w,fe),Ee===null?ee=Ne:Ee.sibling=Ne,Ee=Ne);return Re&&ol(O,fe),ee}for(ue=i(ue);!Ne.done;fe++,Ne=R.next())Ne=M(ue,O,fe,Ne.value,X),Ne!==null&&(e&&Ne.alternate!==null&&ue.delete(Ne.key===null?fe:Ne.key),w=c(Ne,w,fe),Ee===null?ee=Ne:Ee.sibling=Ne,Ee=Ne);return e&&ue.forEach(function(J0){return t(O,J0)}),Re&&ol(O,fe),ee}function Be(O,w,R,X){if(typeof R=="object"&&R!==null&&R.type===_&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case T:e:{for(var ee=R.key;w!==null;){if(w.key===ee){if(ee=R.type,ee===_){if(w.tag===7){n(O,w.sibling),X=o(w,R.props.children),X.return=O,O=X;break e}}else if(w.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===ye&&zd(ee)===w.type){n(O,w.sibling),X=o(w,R.props),Pi(X,R),X.return=O,O=X;break e}n(O,w);break}else t(O,w);w=w.sibling}R.type===_?(X=rl(R.props.children,O.mode,X,R.key),X.return=O,O=X):(X=$a(R.type,R.key,R.props,null,O.mode,X),Pi(X,R),X.return=O,O=X)}return y(O);case v:e:{for(ee=R.key;w!==null;){if(w.key===ee)if(w.tag===4&&w.stateNode.containerInfo===R.containerInfo&&w.stateNode.implementation===R.implementation){n(O,w.sibling),X=o(w,R.children||[]),X.return=O,O=X;break e}else{n(O,w);break}else t(O,w);w=w.sibling}X=Pu(R,O.mode,X),X.return=O,O=X}return y(O);case ye:return ee=R._init,R=ee(R._payload),Be(O,w,R,X)}if(ne(R))return he(O,w,R,X);if($(R)){if(ee=$(R),typeof ee!="function")throw Error(u(150));return R=ee.call(R),ce(O,w,R,X)}if(typeof R.then=="function")return Be(O,w,mr(R),X);if(R.$$typeof===H)return Be(O,w,nr(O,R),X);yr(O,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,w!==null&&w.tag===6?(n(O,w.sibling),X=o(w,R),X.return=O,O=X):(n(O,w),X=Fu(R,O.mode,X),X.return=O,O=X),y(O)):n(O,w)}return function(O,w,R,X){try{Fi=0;var ee=Be(O,w,R,X);return Jl=null,ee}catch(ue){if(ue===qi||ue===ir)throw ue;var Ee=Dt(29,ue,null,O.mode);return Ee.lanes=X,Ee.return=O,Ee}finally{}}}var Fl=Dd(!0),kd=Dd(!1),Kt=G(null),rn=null;function Ln(e){var t=e.alternate;E(tt,tt.current&1),E(Kt,e),rn===null&&(t===null||Gl.current!==null||t.memoizedState!==null)&&(rn=e)}function Md(e){if(e.tag===22){if(E(tt,tt.current),E(Kt,e),rn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(rn=e)}}else Hn()}function Hn(){E(tt,tt.current),E(Kt,Kt.current)}function An(e){I(Kt),rn===e&&(rn=null),I(tt)}var tt=G(0);function gr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||xs(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ro(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:m({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var zo={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=Ut(),o=jn(i);o.payload=t,n!=null&&(o.callback=n),t=Un(e,o,i),t!==null&&(Bt(t,e,i),Vi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=Ut(),o=jn(i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Un(e,o,i),t!==null&&(Bt(t,e,i),Vi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ut(),i=jn(n);i.tag=2,t!=null&&(i.callback=t),t=Un(e,i,n),t!==null&&(Bt(t,e,n),Vi(t,e,n))}};function jd(e,t,n,i,o,c,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,y):t.prototype&&t.prototype.isPureReactComponent?!Di(n,i)||!Di(o,c):!0}function Ud(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&zo.enqueueReplaceState(t,t.state,null)}function ml(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=m({},n));for(var o in e)n[o]===void 0&&(n[o]=e[o])}return n}var br=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Bd(e){br(e)}function Ld(e){console.error(e)}function Hd(e){br(e)}function xr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function qd(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function Do(e,t,n){return n=jn(n),n.tag=3,n.payload={element:null},n.callback=function(){xr(e,t)},n}function Yd(e){return e=jn(e),e.tag=3,e}function Vd(e,t,n,i){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var c=i.value;e.payload=function(){return o(c)},e.callback=function(){qd(t,n,i)}}var y=n.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){qd(t,n,i),typeof o!="function"&&(Qn===null?Qn=new Set([this]):Qn.add(this));var x=i.stack;this.componentDidCatch(i.value,{componentStack:x!==null?x:""})})}function Jg(e,t,n,i,o){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&Bi(t,n,o,!0),n=Kt.current,n!==null){switch(n.tag){case 13:return rn===null?ns():n.alternate===null&&Ke===0&&(Ke=3),n.flags&=-257,n.flags|=65536,n.lanes=o,i===ro?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),is(e,i,o)),!1;case 22:return n.flags|=65536,i===ro?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),is(e,i,o)),!1}throw Error(u(435,n.tag))}return is(e,i,o),ns(),!1}if(Re)return t=Kt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=o,i!==Wu&&(e=Error(u(422),{cause:i}),Ui(Xt(e,n)))):(i!==Wu&&(t=Error(u(423),{cause:i}),Ui(Xt(t,n))),e=e.current.alternate,e.flags|=65536,o&=-o,e.lanes|=o,i=Xt(i,n),o=Do(e.stateNode,i,o),so(e,o),Ke!==4&&(Ke=2)),!1;var c=Error(u(520),{cause:i});if(c=Xt(c,n),la===null?la=[c]:la.push(c),Ke!==4&&(Ke=2),t===null)return!0;i=Xt(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,e=Do(n.stateNode,i,e),so(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Qn===null||!Qn.has(c))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Yd(o),Vd(o,e,n,i),so(n,o),!1}n=n.return}while(n!==null);return!1}var Xd=Error(u(461)),it=!1;function ut(e,t,n,i){t.child=e===null?kd(t,null,n,i):Fl(t,e.child,n,i)}function Gd(e,t,n,i,o){n=n.render;var c=t.ref;if("ref"in i){var y={};for(var x in i)x!=="ref"&&(y[x]=i[x])}else y=i;return dl(t),i=mo(e,t,n,y,c,o),x=yo(),e!==null&&!it?(go(e,t,o),wn(e,t,o)):(Re&&x&&Iu(t),t.flags|=1,ut(e,t,i,o),t.child)}function Qd(e,t,n,i,o){if(e===null){var c=n.type;return typeof c=="function"&&!Ju(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,Zd(e,t,c,i,o)):(e=$a(n.type,null,i,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!qo(e,o)){var y=c.memoizedProps;if(n=n.compare,n=n!==null?n:Di,n(y,i)&&e.ref===t.ref)return wn(e,t,o)}return t.flags|=1,e=bn(c,i),e.ref=t.ref,e.return=t,t.child=e}function Zd(e,t,n,i,o){if(e!==null){var c=e.memoizedProps;if(Di(c,i)&&e.ref===t.ref)if(it=!1,t.pendingProps=i=c,qo(e,o))(e.flags&131072)!==0&&(it=!0);else return t.lanes=e.lanes,wn(e,t,o)}return ko(e,t,n,i,o)}function Kd(e,t,n){var i=t.pendingProps,o=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|n:n,e!==null){for(o=t.child=e.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return Jd(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&lr(t,c!==null?c.cachePool:null),c!==null?Zf(t,c):fo(),Md(t);else return t.lanes=t.childLanes=536870912,Jd(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(lr(t,c.cachePool),Zf(t,c),Hn(),t.memoizedState=null):(e!==null&&lr(t,null),fo(),Hn());return ut(e,t,o,n),t.child}function Jd(e,t,n,i){var o=ao();return o=o===null?null:{parent:et._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},e!==null&&lr(t,null),fo(),Md(t),e!==null&&Bi(e,t,i,!0),null}function Sr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(u(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function ko(e,t,n,i,o){return dl(t),n=mo(e,t,n,i,void 0,o),i=yo(),e!==null&&!it?(go(e,t,o),wn(e,t,o)):(Re&&i&&Iu(t),t.flags|=1,ut(e,t,n,o),t.child)}function Fd(e,t,n,i,o,c){return dl(t),t.updateQueue=null,n=Jf(t,i,n,o),Kf(e),i=yo(),e!==null&&!it?(go(e,t,c),wn(e,t,c)):(Re&&i&&Iu(t),t.flags|=1,ut(e,t,n,c),t.child)}function Pd(e,t,n,i,o){if(dl(t),t.stateNode===null){var c=Hl,y=n.contextType;typeof y=="object"&&y!==null&&(c=ft(y)),c=new n(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=zo,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},uo(t),y=n.contextType,c.context=typeof y=="object"&&y!==null?ft(y):Hl,c.state=t.memoizedState,y=n.getDerivedStateFromProps,typeof y=="function"&&(Ro(t,n,y,i),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(y=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),y!==c.state&&zo.enqueueReplaceState(c,c.state,null),Gi(t,i,c,o),Xi(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var x=t.memoizedProps,A=ml(n,x);c.props=A;var D=c.context,V=n.contextType;y=Hl,typeof V=="object"&&V!==null&&(y=ft(V));var Q=n.getDerivedStateFromProps;V=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",x=t.pendingProps!==x,V||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(x||D!==y)&&Ud(t,c,i,y),Mn=!1;var k=t.memoizedState;c.state=k,Gi(t,i,c,o),Xi(),D=t.memoizedState,x||k!==D||Mn?(typeof Q=="function"&&(Ro(t,n,Q,i),D=t.memoizedState),(A=Mn||jd(t,n,A,i,k,D,y))?(V||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=D),c.props=i,c.state=D,c.context=y,i=A):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,oo(e,t),y=t.memoizedProps,V=ml(n,y),c.props=V,Q=t.pendingProps,k=c.context,D=n.contextType,A=Hl,typeof D=="object"&&D!==null&&(A=ft(D)),x=n.getDerivedStateFromProps,(D=typeof x=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y!==Q||k!==A)&&Ud(t,c,i,A),Mn=!1,k=t.memoizedState,c.state=k,Gi(t,i,c,o),Xi();var M=t.memoizedState;y!==Q||k!==M||Mn||e!==null&&e.dependencies!==null&&tr(e.dependencies)?(typeof x=="function"&&(Ro(t,n,x,i),M=t.memoizedState),(V=Mn||jd(t,n,V,i,k,M,A)||e!==null&&e.dependencies!==null&&tr(e.dependencies))?(D||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,M,A),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,M,A)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=M),c.props=i,c.state=M,c.context=A,i=V):(typeof c.componentDidUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,Sr(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=Fl(t,e.child,null,o),t.child=Fl(t,null,n,o)):ut(e,t,n,o),t.memoizedState=c.state,e=t.child):e=wn(e,t,o),e}function Id(e,t,n,i){return ji(),t.flags|=256,ut(e,t,n,i),t.child}var Mo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jo(e){return{baseLanes:e,cachePool:Lf()}}function Uo(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Jt),e}function $d(e,t,n){var i=t.pendingProps,o=!1,c=(t.flags&128)!==0,y;if((y=c)||(y=e!==null&&e.memoizedState===null?!1:(tt.current&2)!==0),y&&(o=!0,t.flags&=-129),y=(t.flags&32)!==0,t.flags&=-33,e===null){if(Re){if(o?Ln(t):Hn(),Re){var x=Ze,A;if(A=x){e:{for(A=x,x=an;A.nodeType!==8;){if(!x){x=null;break e}if(A=en(A.nextSibling),A===null){x=null;break e}}x=A}x!==null?(t.memoizedState={dehydrated:x,treeContext:ul!==null?{id:xn,overflow:Sn}:null,retryLane:536870912,hydrationErrors:null},A=Dt(18,null,null,0),A.stateNode=x,A.return=t,t.child=A,yt=t,Ze=null,A=!0):A=!1}A||cl(t)}if(x=t.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return xs(x)?t.lanes=32:t.lanes=536870912,null;An(t)}return x=i.children,i=i.fallback,o?(Hn(),o=t.mode,x=vr({mode:"hidden",children:x},o),i=rl(i,o,n,null),x.return=t,i.return=t,x.sibling=i,t.child=x,o=t.child,o.memoizedState=jo(n),o.childLanes=Uo(e,y,n),t.memoizedState=Mo,i):(Ln(t),Bo(t,x))}if(A=e.memoizedState,A!==null&&(x=A.dehydrated,x!==null)){if(c)t.flags&256?(Ln(t),t.flags&=-257,t=Lo(e,t,n)):t.memoizedState!==null?(Hn(),t.child=e.child,t.flags|=128,t=null):(Hn(),o=i.fallback,x=t.mode,i=vr({mode:"visible",children:i.children},x),o=rl(o,x,n,null),o.flags|=2,i.return=t,o.return=t,i.sibling=o,t.child=i,Fl(t,e.child,null,n),i=t.child,i.memoizedState=jo(n),i.childLanes=Uo(e,y,n),t.memoizedState=Mo,t=o);else if(Ln(t),xs(x)){if(y=x.nextSibling&&x.nextSibling.dataset,y)var D=y.dgst;y=D,i=Error(u(419)),i.stack="",i.digest=y,Ui({value:i,source:null,stack:null}),t=Lo(e,t,n)}else if(it||Bi(e,t,n,!1),y=(n&e.childLanes)!==0,it||y){if(y=qe,y!==null&&(i=n&-n,i=(i&42)!==0?1:xu(i),i=(i&(y.suspendedLanes|n))!==0?0:i,i!==0&&i!==A.retryLane))throw A.retryLane=i,Ll(e,i),Bt(y,e,i),Xd;x.data==="$?"||ns(),t=Lo(e,t,n)}else x.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=A.treeContext,Ze=en(x.nextSibling),yt=t,Re=!0,sl=null,an=!1,e!==null&&(Qt[Zt++]=xn,Qt[Zt++]=Sn,Qt[Zt++]=ul,xn=e.id,Sn=e.overflow,ul=t),t=Bo(t,i.children),t.flags|=4096);return t}return o?(Hn(),o=i.fallback,x=t.mode,A=e.child,D=A.sibling,i=bn(A,{mode:"hidden",children:i.children}),i.subtreeFlags=A.subtreeFlags&65011712,D!==null?o=bn(D,o):(o=rl(o,x,n,null),o.flags|=2),o.return=t,i.return=t,i.sibling=o,t.child=i,i=o,o=t.child,x=e.child.memoizedState,x===null?x=jo(n):(A=x.cachePool,A!==null?(D=et._currentValue,A=A.parent!==D?{parent:D,pool:D}:A):A=Lf(),x={baseLanes:x.baseLanes|n,cachePool:A}),o.memoizedState=x,o.childLanes=Uo(e,y,n),t.memoizedState=Mo,i):(Ln(t),n=e.child,e=n.sibling,n=bn(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(y=t.deletions,y===null?(t.deletions=[e],t.flags|=16):y.push(e)),t.child=n,t.memoizedState=null,n)}function Bo(e,t){return t=vr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function vr(e,t){return e=Dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Lo(e,t,n){return Fl(t,e.child,null,n),e=Bo(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wd(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),to(e.return,t,n)}function Ho(e,t,n,i,o){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:o}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=n,c.tailMode=o)}function eh(e,t,n){var i=t.pendingProps,o=i.revealOrder,c=i.tail;if(ut(e,t,i.children,n),i=tt.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Wd(e,n,t);else if(e.tag===19)Wd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(E(tt,i),o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&gr(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ho(t,!1,o,n,c);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&gr(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ho(t,!0,n,null,c);break;case"together":Ho(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Gn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Bi(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,n=bn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=bn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qo(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&tr(e)))}function Fg(e,t,n){switch(t.tag){case 3:Ce(t,t.stateNode.containerInfo),kn(t,et,e.memoizedState.cache),ji();break;case 27:case 5:nn(t);break;case 4:Ce(t,t.stateNode.containerInfo);break;case 10:kn(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Ln(t),t.flags|=128,null):(n&t.child.childLanes)!==0?$d(e,t,n):(Ln(t),e=wn(e,t,n),e!==null?e.sibling:null);Ln(t);break;case 19:var o=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(Bi(e,t,n,!1),i=(n&t.childLanes)!==0),o){if(i)return eh(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),E(tt,tt.current),i)break;return null;case 22:case 23:return t.lanes=0,Kd(e,t,n);case 24:kn(t,et,e.memoizedState.cache)}return wn(e,t,n)}function th(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)it=!0;else{if(!qo(e,n)&&(t.flags&128)===0)return it=!1,Fg(e,t,n);it=(e.flags&131072)!==0}else it=!1,Re&&(t.flags&1048576)!==0&&zf(t,er,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,o=i._init;if(i=o(i._payload),t.type=i,typeof i=="function")Ju(i)?(e=ml(i,e),t.tag=1,t=Pd(null,t,i,e,n)):(t.tag=0,t=ko(null,t,i,e,n));else{if(i!=null){if(o=i.$$typeof,o===P){t.tag=11,t=Gd(null,t,i,e,n);break e}else if(o===oe){t.tag=14,t=Qd(null,t,i,e,n);break e}}throw t=W(i)||i,Error(u(306,t,""))}}return t;case 0:return ko(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,o=ml(i,t.pendingProps),Pd(e,t,i,o,n);case 3:e:{if(Ce(t,t.stateNode.containerInfo),e===null)throw Error(u(387));i=t.pendingProps;var c=t.memoizedState;o=c.element,oo(e,t),Gi(t,i,null,n);var y=t.memoizedState;if(i=y.cache,kn(t,et,i),i!==c.cache&&no(t,[et],n,!0),Xi(),i=y.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:y.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Id(e,t,i,n);break e}else if(i!==o){o=Xt(Error(u(424)),t),Ui(o),t=Id(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ze=en(e.firstChild),yt=t,Re=!0,sl=null,an=!0,n=kd(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ji(),i===o){t=wn(e,t,n);break e}ut(e,t,i,n)}t=t.child}return t;case 26:return Sr(e,t),e===null?(n=ap(t.type,null,t.pendingProps,null))?t.memoizedState=n:Re||(n=t.type,e=t.pendingProps,i=jr(le.current).createElement(n),i[ct]=t,i[bt]=e,st(i,n,e),lt(i),t.stateNode=i):t.memoizedState=ap(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return nn(t),e===null&&Re&&(i=t.stateNode=np(t.type,t.pendingProps,le.current),yt=t,an=!0,o=Ze,Jn(t.type)?(Ss=o,Ze=en(i.firstChild)):Ze=o),ut(e,t,t.pendingProps.children,n),Sr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Re&&((o=i=Ze)&&(i=T0(i,t.type,t.pendingProps,an),i!==null?(t.stateNode=i,yt=t,Ze=en(i.firstChild),an=!1,o=!0):o=!1),o||cl(t)),nn(t),o=t.type,c=t.pendingProps,y=e!==null?e.memoizedProps:null,i=c.children,ys(o,c)?i=null:y!==null&&ys(o,y)&&(t.flags|=32),t.memoizedState!==null&&(o=mo(e,t,Yg,null,null,n),da._currentValue=o),Sr(e,t),ut(e,t,i,n),t.child;case 6:return e===null&&Re&&((e=n=Ze)&&(n=A0(n,t.pendingProps,an),n!==null?(t.stateNode=n,yt=t,Ze=null,e=!0):e=!1),e||cl(t)),null;case 13:return $d(e,t,n);case 4:return Ce(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Fl(t,null,i,n):ut(e,t,i,n),t.child;case 11:return Gd(e,t,t.type,t.pendingProps,n);case 7:return ut(e,t,t.pendingProps,n),t.child;case 8:return ut(e,t,t.pendingProps.children,n),t.child;case 12:return ut(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,kn(t,t.type,i.value),ut(e,t,i.children,n),t.child;case 9:return o=t.type._context,i=t.pendingProps.children,dl(t),o=ft(o),i=i(o),t.flags|=1,ut(e,t,i,n),t.child;case 14:return Qd(e,t,t.type,t.pendingProps,n);case 15:return Zd(e,t,t.type,t.pendingProps,n);case 19:return eh(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=vr(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=bn(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Kd(e,t,n);case 24:return dl(t),i=ft(et),e===null?(o=ao(),o===null&&(o=qe,c=lo(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=n),o=c),t.memoizedState={parent:i,cache:o},uo(t),kn(t,et,o)):((e.lanes&n)!==0&&(oo(e,t),Gi(t,null,null,n),Xi()),o=e.memoizedState,c=t.memoizedState,o.parent!==i?(o={parent:i,cache:i},t.memoizedState=o,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=o),kn(t,et,i)):(i=c.cache,kn(t,et,i),i!==o.cache&&no(t,[et],n,!0))),ut(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function On(e){e.flags|=4}function nh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!cp(t)){if(t=Kt.current,t!==null&&((_e&4194048)===_e?rn!==null:(_e&62914560)!==_e&&(_e&536870912)===0||t!==rn))throw Yi=ro,Hf;e.flags|=8192}}function Er(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?kc():536870912,e.lanes|=t,Wl|=t)}function Ii(e,t){if(!Re)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function Qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,i|=o.subtreeFlags&65011712,i|=o.flags&65011712,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,i|=o.subtreeFlags,i|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function Pg(e,t,n){var i=t.pendingProps;switch($u(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qe(t),null;case 1:return Qe(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),En(et),mt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Mi(t)?On(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Mf())),Qe(t),null;case 26:return n=t.memoizedState,e===null?(On(t),n!==null?(Qe(t),nh(t,n)):(Qe(t),t.flags&=-16777217)):n?n!==e.memoizedState?(On(t),Qe(t),nh(t,n)):(Qe(t),t.flags&=-16777217):(e.memoizedProps!==i&&On(t),Qe(t),t.flags&=-16777217),null;case 27:pn(t),n=le.current;var o=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&On(t);else{if(!i){if(t.stateNode===null)throw Error(u(166));return Qe(t),null}e=te.current,Mi(t)?Df(t):(e=np(o,i,n),t.stateNode=e,On(t))}return Qe(t),null;case 5:if(pn(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&On(t);else{if(!i){if(t.stateNode===null)throw Error(u(166));return Qe(t),null}if(e=te.current,Mi(t))Df(t);else{switch(o=jr(le.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?o.createElement("select",{is:i.is}):o.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?o.createElement(n,{is:i.is}):o.createElement(n)}}e[ct]=t,e[bt]=i;e:for(o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;o.sibling===null;){if(o.return===null||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(st(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&On(t)}}return Qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&On(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(u(166));if(e=le.current,Mi(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,o=yt,o!==null)switch(o.tag){case 27:case 5:i=o.memoizedProps}e[ct]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||Fh(e.nodeValue,n)),e||cl(t)}else e=jr(e).createTextNode(i),e[ct]=t,t.stateNode=e}return Qe(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(o=Mi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!o)throw Error(u(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(u(317));o[ct]=t}else ji(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Qe(t),o=!1}else o=Mf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return t.flags&256?(An(t),t):(An(t),null)}if(An(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,o=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(o=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==o&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Er(t,t.updateQueue),Qe(t),null;case 4:return mt(),e===null&&fs(t.stateNode.containerInfo),Qe(t),null;case 10:return En(t.type),Qe(t),null;case 19:if(I(tt),o=t.memoizedState,o===null)return Qe(t),null;if(i=(t.flags&128)!==0,c=o.rendering,c===null)if(i)Ii(o,!1);else{if(Ke!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=gr(e),c!==null){for(t.flags|=128,Ii(o,!1),e=c.updateQueue,t.updateQueue=e,Er(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Rf(n,e),n=n.sibling;return E(tt,tt.current&1|2),t.child}e=e.sibling}o.tail!==null&&Ct()>wr&&(t.flags|=128,i=!0,Ii(o,!1),t.lanes=4194304)}else{if(!i)if(e=gr(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,Er(t,e),Ii(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!Re)return Qe(t),null}else 2*Ct()-o.renderingStartTime>wr&&n!==536870912&&(t.flags|=128,i=!0,Ii(o,!1),t.lanes=4194304);o.isBackwards?(c.sibling=t.child,t.child=c):(e=o.last,e!==null?e.sibling=c:t.child=c,o.last=c)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ct(),t.sibling=null,e=tt.current,E(tt,i?e&1|2:e&1),t):(Qe(t),null);case 22:case 23:return An(t),ho(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(Qe(t),t.subtreeFlags&6&&(t.flags|=8192)):Qe(t),n=t.updateQueue,n!==null&&Er(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&I(hl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),En(et),Qe(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function Ig(e,t){switch($u(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return En(et),mt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return pn(t),null;case 13:if(An(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));ji()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(tt),null;case 4:return mt(),null;case 10:return En(t.type),null;case 22:case 23:return An(t),ho(),e!==null&&I(hl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return En(et),null;case 25:return null;default:return null}}function lh(e,t){switch($u(t),t.tag){case 3:En(et),mt();break;case 26:case 27:case 5:pn(t);break;case 4:mt();break;case 13:An(t);break;case 19:I(tt);break;case 10:En(t.type);break;case 22:case 23:An(t),ho(),e!==null&&I(hl);break;case 24:En(et)}}function $i(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var o=i.next;n=o;do{if((n.tag&e)===e){i=void 0;var c=n.create,y=n.inst;i=c(),y.destroy=i}n=n.next}while(n!==o)}}catch(x){Le(t,t.return,x)}}function qn(e,t,n){try{var i=t.updateQueue,o=i!==null?i.lastEffect:null;if(o!==null){var c=o.next;i=c;do{if((i.tag&e)===e){var y=i.inst,x=y.destroy;if(x!==void 0){y.destroy=void 0,o=t;var A=n,D=x;try{D()}catch(V){Le(o,A,V)}}}i=i.next}while(i!==c)}}catch(V){Le(t,t.return,V)}}function ih(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Qf(t,n)}catch(i){Le(e,e.return,i)}}}function ah(e,t,n){n.props=ml(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){Le(e,t,i)}}function Wi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(o){Le(e,t,o)}}function un(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(o){Le(e,t,o)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){Le(e,t,o)}else n.current=null}function rh(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(o){Le(e,e.return,o)}}function Yo(e,t,n){try{var i=e.stateNode;b0(i,e.type,n,t),i[bt]=t}catch(o){Le(e,e.return,o)}}function uh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Jn(e.type)||e.tag===4}function Vo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||uh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Jn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xo(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Mr));else if(i!==4&&(i===27&&Jn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Xo(e,t,n),e=e.sibling;e!==null;)Xo(e,t,n),e=e.sibling}function Tr(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&Jn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Tr(e,t,n),e=e.sibling;e!==null;)Tr(e,t,n),e=e.sibling}function oh(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);st(t,i,n),t[ct]=e,t[bt]=n}catch(c){Le(e,e.return,c)}}var _n=!1,Fe=!1,Go=!1,sh=typeof WeakSet=="function"?WeakSet:Set,at=null;function $g(e,t){if(e=e.containerInfo,ps=Yr,e=Sf(e),Yu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var o=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var y=0,x=-1,A=-1,D=0,V=0,Q=e,k=null;t:for(;;){for(var M;Q!==n||o!==0&&Q.nodeType!==3||(x=y+o),Q!==c||i!==0&&Q.nodeType!==3||(A=y+i),Q.nodeType===3&&(y+=Q.nodeValue.length),(M=Q.firstChild)!==null;)k=Q,Q=M;for(;;){if(Q===e)break t;if(k===n&&++D===o&&(x=y),k===c&&++V===i&&(A=y),(M=Q.nextSibling)!==null)break;Q=k,k=Q.parentNode}Q=M}n=x===-1||A===-1?null:{start:x,end:A}}else n=null}n=n||{start:0,end:0}}else n=null;for(ms={focusedElem:e,selectionRange:n},Yr=!1,at=t;at!==null;)if(t=at,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,at=e;else for(;at!==null;){switch(t=at,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,o=c.memoizedProps,c=c.memoizedState,i=n.stateNode;try{var he=ml(n.type,o,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(he,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(ce){Le(n,n.return,ce)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)bs(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":bs(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,at=e;break}at=t.return}}function ch(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Yn(e,n),i&4&&$i(5,n);break;case 1:if(Yn(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(y){Le(n,n.return,y)}else{var o=ml(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(y){Le(n,n.return,y)}}i&64&&ih(n),i&512&&Wi(n,n.return);break;case 3:if(Yn(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Qf(e,t)}catch(y){Le(n,n.return,y)}}break;case 27:t===null&&i&4&&oh(n);case 26:case 5:Yn(e,n),t===null&&i&4&&rh(n),i&512&&Wi(n,n.return);break;case 12:Yn(e,n);break;case 13:Yn(e,n),i&4&&hh(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=u0.bind(null,n),w0(e,n))));break;case 22:if(i=n.memoizedState!==null||_n,!i){t=t!==null&&t.memoizedState!==null||Fe,o=_n;var c=Fe;_n=i,(Fe=t)&&!c?Vn(e,n,(n.subtreeFlags&8772)!==0):Yn(e,n),_n=o,Fe=c}break;case 30:break;default:Yn(e,n)}}function fh(e){var t=e.alternate;t!==null&&(e.alternate=null,fh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Eu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ge=null,vt=!1;function Cn(e,t,n){for(n=n.child;n!==null;)dh(e,t,n),n=n.sibling}function dh(e,t,n){if(Z&&typeof Z.onCommitFiberUnmount=="function")try{Z.onCommitFiberUnmount(Y,n)}catch{}switch(n.tag){case 26:Fe||un(n,t),Cn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Fe||un(n,t);var i=Ge,o=vt;Jn(n.type)&&(Ge=n.stateNode,vt=!1),Cn(e,t,n),oa(n.stateNode),Ge=i,vt=o;break;case 5:Fe||un(n,t);case 6:if(i=Ge,o=vt,Ge=null,Cn(e,t,n),Ge=i,vt=o,Ge!==null)if(vt)try{(Ge.nodeType===9?Ge.body:Ge.nodeName==="HTML"?Ge.ownerDocument.body:Ge).removeChild(n.stateNode)}catch(c){Le(n,t,c)}else try{Ge.removeChild(n.stateNode)}catch(c){Le(n,t,c)}break;case 18:Ge!==null&&(vt?(e=Ge,ep(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),ya(e)):ep(Ge,n.stateNode));break;case 4:i=Ge,o=vt,Ge=n.stateNode.containerInfo,vt=!0,Cn(e,t,n),Ge=i,vt=o;break;case 0:case 11:case 14:case 15:Fe||qn(2,n,t),Fe||qn(4,n,t),Cn(e,t,n);break;case 1:Fe||(un(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&ah(n,t,i)),Cn(e,t,n);break;case 21:Cn(e,t,n);break;case 22:Fe=(i=Fe)||n.memoizedState!==null,Cn(e,t,n),Fe=i;break;default:Cn(e,t,n)}}function hh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ya(e)}catch(n){Le(t,t.return,n)}}function Wg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new sh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new sh),t;default:throw Error(u(435,e.tag))}}function Qo(e,t){var n=Wg(e);t.forEach(function(i){var o=o0.bind(null,e,i);n.has(i)||(n.add(i),i.then(o,o))})}function kt(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var o=n[i],c=e,y=t,x=y;e:for(;x!==null;){switch(x.tag){case 27:if(Jn(x.type)){Ge=x.stateNode,vt=!1;break e}break;case 5:Ge=x.stateNode,vt=!1;break e;case 3:case 4:Ge=x.stateNode.containerInfo,vt=!0;break e}x=x.return}if(Ge===null)throw Error(u(160));dh(c,y,o),Ge=null,vt=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)ph(t,e),t=t.sibling}var Wt=null;function ph(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:kt(t,e),Mt(e),i&4&&(qn(3,e,e.return),$i(3,e),qn(5,e,e.return));break;case 1:kt(t,e),Mt(e),i&512&&(Fe||n===null||un(n,n.return)),i&64&&_n&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var o=Wt;if(kt(t,e),Mt(e),i&512&&(Fe||n===null||un(n,n.return)),i&4){var c=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(i){case"title":c=o.getElementsByTagName("title")[0],(!c||c[Ti]||c[ct]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(i),o.head.insertBefore(c,o.querySelector("head > title"))),st(c,i,n),c[ct]=e,lt(c),i=c;break e;case"link":var y=op("link","href",o).get(i+(n.href||""));if(y){for(var x=0;x<y.length;x++)if(c=y[x],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){y.splice(x,1);break t}}c=o.createElement(i),st(c,i,n),o.head.appendChild(c);break;case"meta":if(y=op("meta","content",o).get(i+(n.content||""))){for(x=0;x<y.length;x++)if(c=y[x],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){y.splice(x,1);break t}}c=o.createElement(i),st(c,i,n),o.head.appendChild(c);break;default:throw Error(u(468,i))}c[ct]=e,lt(c),i=c}e.stateNode=i}else sp(o,e.type,e.stateNode);else e.stateNode=up(o,i,e.memoizedProps);else c!==i?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,i===null?sp(o,e.type,e.stateNode):up(o,i,e.memoizedProps)):i===null&&e.stateNode!==null&&Yo(e,e.memoizedProps,n.memoizedProps)}break;case 27:kt(t,e),Mt(e),i&512&&(Fe||n===null||un(n,n.return)),n!==null&&i&4&&Yo(e,e.memoizedProps,n.memoizedProps);break;case 5:if(kt(t,e),Mt(e),i&512&&(Fe||n===null||un(n,n.return)),e.flags&32){o=e.stateNode;try{zl(o,"")}catch(M){Le(e,e.return,M)}}i&4&&e.stateNode!=null&&(o=e.memoizedProps,Yo(e,o,n!==null?n.memoizedProps:o)),i&1024&&(Go=!0);break;case 6:if(kt(t,e),Mt(e),i&4){if(e.stateNode===null)throw Error(u(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(M){Le(e,e.return,M)}}break;case 3:if(Lr=null,o=Wt,Wt=Ur(t.containerInfo),kt(t,e),Wt=o,Mt(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{ya(t.containerInfo)}catch(M){Le(e,e.return,M)}Go&&(Go=!1,mh(e));break;case 4:i=Wt,Wt=Ur(e.stateNode.containerInfo),kt(t,e),Mt(e),Wt=i;break;case 12:kt(t,e),Mt(e);break;case 13:kt(t,e),Mt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Io=Ct()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,Qo(e,i)));break;case 22:o=e.memoizedState!==null;var A=n!==null&&n.memoizedState!==null,D=_n,V=Fe;if(_n=D||o,Fe=V||A,kt(t,e),Fe=V,_n=D,Mt(e),i&8192)e:for(t=e.stateNode,t._visibility=o?t._visibility&-2:t._visibility|1,o&&(n===null||A||_n||Fe||yl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){A=n=t;try{if(c=A.stateNode,o)y=c.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{x=A.stateNode;var Q=A.memoizedProps.style,k=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;x.style.display=k==null||typeof k=="boolean"?"":(""+k).trim()}}catch(M){Le(A,A.return,M)}}}else if(t.tag===6){if(n===null){A=t;try{A.stateNode.nodeValue=o?"":A.memoizedProps}catch(M){Le(A,A.return,M)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Qo(e,n))));break;case 19:kt(t,e),Mt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,Qo(e,i)));break;case 30:break;case 21:break;default:kt(t,e),Mt(e)}}function Mt(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(uh(i)){n=i;break}i=i.return}if(n==null)throw Error(u(160));switch(n.tag){case 27:var o=n.stateNode,c=Vo(e);Tr(e,c,o);break;case 5:var y=n.stateNode;n.flags&32&&(zl(y,""),n.flags&=-33);var x=Vo(e);Tr(e,x,y);break;case 3:case 4:var A=n.stateNode.containerInfo,D=Vo(e);Xo(e,D,A);break;default:throw Error(u(161))}}catch(V){Le(e,e.return,V)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function mh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;mh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Yn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)ch(e,t.alternate,t),t=t.sibling}function yl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:qn(4,t,t.return),yl(t);break;case 1:un(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&ah(t,t.return,n),yl(t);break;case 27:oa(t.stateNode);case 26:case 5:un(t,t.return),yl(t);break;case 22:t.memoizedState===null&&yl(t);break;case 30:yl(t);break;default:yl(t)}e=e.sibling}}function Vn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,o=e,c=t,y=c.flags;switch(c.tag){case 0:case 11:case 15:Vn(o,c,n),$i(4,c);break;case 1:if(Vn(o,c,n),i=c,o=i.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(D){Le(i,i.return,D)}if(i=c,o=i.updateQueue,o!==null){var x=i.stateNode;try{var A=o.shared.hiddenCallbacks;if(A!==null)for(o.shared.hiddenCallbacks=null,o=0;o<A.length;o++)Gf(A[o],x)}catch(D){Le(i,i.return,D)}}n&&y&64&&ih(c),Wi(c,c.return);break;case 27:oh(c);case 26:case 5:Vn(o,c,n),n&&i===null&&y&4&&rh(c),Wi(c,c.return);break;case 12:Vn(o,c,n);break;case 13:Vn(o,c,n),n&&y&4&&hh(o,c);break;case 22:c.memoizedState===null&&Vn(o,c,n),Wi(c,c.return);break;case 30:break;default:Vn(o,c,n)}t=t.sibling}}function Zo(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Li(n))}function Ko(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Li(e))}function on(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)yh(e,t,n,i),t=t.sibling}function yh(e,t,n,i){var o=t.flags;switch(t.tag){case 0:case 11:case 15:on(e,t,n,i),o&2048&&$i(9,t);break;case 1:on(e,t,n,i);break;case 3:on(e,t,n,i),o&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Li(e)));break;case 12:if(o&2048){on(e,t,n,i),e=t.stateNode;try{var c=t.memoizedProps,y=c.id,x=c.onPostCommit;typeof x=="function"&&x(y,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(A){Le(t,t.return,A)}}else on(e,t,n,i);break;case 13:on(e,t,n,i);break;case 23:break;case 22:c=t.stateNode,y=t.alternate,t.memoizedState!==null?c._visibility&2?on(e,t,n,i):ea(e,t):c._visibility&2?on(e,t,n,i):(c._visibility|=2,Pl(e,t,n,i,(t.subtreeFlags&10256)!==0)),o&2048&&Zo(y,t);break;case 24:on(e,t,n,i),o&2048&&Ko(t.alternate,t);break;default:on(e,t,n,i)}}function Pl(e,t,n,i,o){for(o=o&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,y=t,x=n,A=i,D=y.flags;switch(y.tag){case 0:case 11:case 15:Pl(c,y,x,A,o),$i(8,y);break;case 23:break;case 22:var V=y.stateNode;y.memoizedState!==null?V._visibility&2?Pl(c,y,x,A,o):ea(c,y):(V._visibility|=2,Pl(c,y,x,A,o)),o&&D&2048&&Zo(y.alternate,y);break;case 24:Pl(c,y,x,A,o),o&&D&2048&&Ko(y.alternate,y);break;default:Pl(c,y,x,A,o)}t=t.sibling}}function ea(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,o=i.flags;switch(i.tag){case 22:ea(n,i),o&2048&&Zo(i.alternate,i);break;case 24:ea(n,i),o&2048&&Ko(i.alternate,i);break;default:ea(n,i)}t=t.sibling}}var ta=8192;function Il(e){if(e.subtreeFlags&ta)for(e=e.child;e!==null;)gh(e),e=e.sibling}function gh(e){switch(e.tag){case 26:Il(e),e.flags&ta&&e.memoizedState!==null&&L0(Wt,e.memoizedState,e.memoizedProps);break;case 5:Il(e);break;case 3:case 4:var t=Wt;Wt=Ur(e.stateNode.containerInfo),Il(e),Wt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=ta,ta=16777216,Il(e),ta=t):Il(e));break;default:Il(e)}}function bh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function na(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];at=i,Sh(i,e)}bh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)xh(e),e=e.sibling}function xh(e){switch(e.tag){case 0:case 11:case 15:na(e),e.flags&2048&&qn(9,e,e.return);break;case 3:na(e);break;case 12:na(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Ar(e)):na(e);break;default:na(e)}}function Ar(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];at=i,Sh(i,e)}bh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:qn(8,t,t.return),Ar(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Ar(t));break;default:Ar(t)}e=e.sibling}}function Sh(e,t){for(;at!==null;){var n=at;switch(n.tag){case 0:case 11:case 15:qn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Li(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,at=i;else e:for(n=e;at!==null;){i=at;var o=i.sibling,c=i.return;if(fh(i),i===n){at=null;break e}if(o!==null){o.return=c,at=o;break e}at=c}}}var e0={getCacheForType:function(e){var t=ft(et),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},t0=typeof WeakMap=="function"?WeakMap:Map,ke=0,qe=null,Ae=null,_e=0,Me=0,jt=null,Xn=!1,$l=!1,Jo=!1,Nn=0,Ke=0,Gn=0,gl=0,Fo=0,Jt=0,Wl=0,la=null,Et=null,Po=!1,Io=0,wr=1/0,Or=null,Qn=null,ot=0,Zn=null,ei=null,ti=0,$o=0,Wo=null,vh=null,ia=0,es=null;function Ut(){if((ke&2)!==0&&_e!==0)return _e&-_e;if(B.T!==null){var e=Vl;return e!==0?e:us()}return Uc()}function Eh(){Jt===0&&(Jt=(_e&536870912)===0||Re?Dc():536870912);var e=Kt.current;return e!==null&&(e.flags|=32),Jt}function Bt(e,t,n){(e===qe&&(Me===2||Me===9)||e.cancelPendingCommit!==null)&&(ni(e,0),Kn(e,_e,Jt,!1)),Ei(e,n),((ke&2)===0||e!==qe)&&(e===qe&&((ke&2)===0&&(gl|=n),Ke===4&&Kn(e,_e,Jt,!1)),sn(e))}function Th(e,t,n){if((ke&6)!==0)throw Error(u(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||qt(e,t),o=i?i0(e,t):ls(e,t,!0),c=i;do{if(o===0){$l&&!i&&Kn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!n0(n)){o=ls(e,t,!1),c=!1;continue}if(o===2){if(c=t,e.errorRecoveryDisabledLanes&c)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){t=y;e:{var x=e;o=la;var A=x.current.memoizedState.isDehydrated;if(A&&(ni(x,y).flags|=256),y=ls(x,y,!1),y!==2){if(Jo&&!A){x.errorRecoveryDisabledLanes|=c,gl|=c,o=4;break e}c=Et,Et=o,c!==null&&(Et===null?Et=c:Et.push.apply(Et,c))}o=y}if(c=!1,o!==2)continue}}if(o===1){ni(e,0),Kn(e,t,0,!0);break}e:{switch(i=e,c=o,c){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:Kn(i,t,Jt,!Xn);break e;case 2:Et=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(o=Io+300-Ct(),10<o)){if(Kn(i,t,Jt,!Xn),nt(i,0,!0)!==0)break e;i.timeoutHandle=$h(Ah.bind(null,i,n,Et,Or,Po,t,Jt,gl,Wl,Xn,c,2,-0,0),o);break e}Ah(i,n,Et,Or,Po,t,Jt,gl,Wl,Xn,c,0,-0,0)}}break}while(!0);sn(e)}function Ah(e,t,n,i,o,c,y,x,A,D,V,Q,k,M){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(fa={stylesheets:null,count:0,unsuspend:B0},gh(t),Q=H0(),Q!==null)){e.cancelPendingCommit=Q(zh.bind(null,e,t,c,n,i,o,y,x,A,V,1,k,M)),Kn(e,c,y,!D);return}zh(e,t,c,n,i,o,y,x,A)}function n0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var o=n[i],c=o.getSnapshot;o=o.value;try{if(!zt(c(),o))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Kn(e,t,n,i){t&=~Fo,t&=~gl,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var o=t;0<o;){var c=31-de(o),y=1<<c;i[c]=-1,o&=~y}n!==0&&Mc(e,n,t)}function _r(){return(ke&6)===0?(aa(0),!1):!0}function ts(){if(Ae!==null){if(Me===0)var e=Ae.return;else e=Ae,vn=fl=null,bo(e),Jl=null,Fi=0,e=Ae;for(;e!==null;)lh(e.alternate,e),e=e.return;Ae=null}}function ni(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,S0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),ts(),qe=e,Ae=n=bn(e.current,null),_e=t,Me=0,jt=null,Xn=!1,$l=qt(e,t),Jo=!1,Wl=Jt=Fo=gl=Gn=Ke=0,Et=la=null,Po=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var o=31-de(i),c=1<<o;t|=e[o],i&=~c}return Nn=t,Fa(),n}function wh(e,t){ve=null,B.H=pr,t===qi||t===ir?(t=Vf(),Me=3):t===Hf?(t=Vf(),Me=4):Me=t===Xd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,jt=t,Ae===null&&(Ke=1,xr(e,Xt(t,e.current)))}function Oh(){var e=B.H;return B.H=pr,e===null?pr:e}function _h(){var e=B.A;return B.A=e0,e}function ns(){Ke=4,Xn||(_e&4194048)!==_e&&Kt.current!==null||($l=!0),(Gn&134217727)===0&&(gl&134217727)===0||qe===null||Kn(qe,_e,Jt,!1)}function ls(e,t,n){var i=ke;ke|=2;var o=Oh(),c=_h();(qe!==e||_e!==t)&&(Or=null,ni(e,t)),t=!1;var y=Ke;e:do try{if(Me!==0&&Ae!==null){var x=Ae,A=jt;switch(Me){case 8:ts(),y=6;break e;case 3:case 2:case 9:case 6:Kt.current===null&&(t=!0);var D=Me;if(Me=0,jt=null,li(e,x,A,D),n&&$l){y=0;break e}break;default:D=Me,Me=0,jt=null,li(e,x,A,D)}}l0(),y=Ke;break}catch(V){wh(e,V)}while(!0);return t&&e.shellSuspendCounter++,vn=fl=null,ke=i,B.H=o,B.A=c,Ae===null&&(qe=null,_e=0,Fa()),y}function l0(){for(;Ae!==null;)Ch(Ae)}function i0(e,t){var n=ke;ke|=2;var i=Oh(),o=_h();qe!==e||_e!==t?(Or=null,wr=Ct()+500,ni(e,t)):$l=qt(e,t);e:do try{if(Me!==0&&Ae!==null){t=Ae;var c=jt;t:switch(Me){case 1:Me=0,jt=null,li(e,t,c,1);break;case 2:case 9:if(qf(c)){Me=0,jt=null,Nh(t);break}t=function(){Me!==2&&Me!==9||qe!==e||(Me=7),sn(e)},c.then(t,t);break e;case 3:Me=7;break e;case 4:Me=5;break e;case 7:qf(c)?(Me=0,jt=null,Nh(t)):(Me=0,jt=null,li(e,t,c,7));break;case 5:var y=null;switch(Ae.tag){case 26:y=Ae.memoizedState;case 5:case 27:var x=Ae;if(!y||cp(y)){Me=0,jt=null;var A=x.sibling;if(A!==null)Ae=A;else{var D=x.return;D!==null?(Ae=D,Cr(D)):Ae=null}break t}}Me=0,jt=null,li(e,t,c,5);break;case 6:Me=0,jt=null,li(e,t,c,6);break;case 8:ts(),Ke=6;break e;default:throw Error(u(462))}}a0();break}catch(V){wh(e,V)}while(!0);return vn=fl=null,B.H=i,B.A=o,ke=n,Ae!==null?0:(qe=null,_e=0,Fa(),Ke)}function a0(){for(;Ae!==null&&!ja();)Ch(Ae)}function Ch(e){var t=th(e.alternate,e,Nn);e.memoizedProps=e.pendingProps,t===null?Cr(e):Ae=t}function Nh(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Fd(n,t,t.pendingProps,t.type,void 0,_e);break;case 11:t=Fd(n,t,t.pendingProps,t.type.render,t.ref,_e);break;case 5:bo(t);default:lh(n,t),t=Ae=Rf(t,Nn),t=th(n,t,Nn)}e.memoizedProps=e.pendingProps,t===null?Cr(e):Ae=t}function li(e,t,n,i){vn=fl=null,bo(t),Jl=null,Fi=0;var o=t.return;try{if(Jg(e,o,t,n,_e)){Ke=1,xr(e,Xt(n,e.current)),Ae=null;return}}catch(c){if(o!==null)throw Ae=o,c;Ke=1,xr(e,Xt(n,e.current)),Ae=null;return}t.flags&32768?(Re||i===1?e=!0:$l||(_e&536870912)!==0?e=!1:(Xn=e=!0,(i===2||i===9||i===3||i===6)&&(i=Kt.current,i!==null&&i.tag===13&&(i.flags|=16384))),Rh(t,e)):Cr(t)}function Cr(e){var t=e;do{if((t.flags&32768)!==0){Rh(t,Xn);return}e=t.return;var n=Pg(t.alternate,t,Nn);if(n!==null){Ae=n;return}if(t=t.sibling,t!==null){Ae=t;return}Ae=t=e}while(t!==null);Ke===0&&(Ke=5)}function Rh(e,t){do{var n=Ig(e.alternate,e);if(n!==null){n.flags&=32767,Ae=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Ae=e;return}Ae=e=n}while(e!==null);Ke=6,Ae=null}function zh(e,t,n,i,o,c,y,x,A){e.cancelPendingCommit=null;do Nr();while(ot!==0);if((ke&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(c=t.lanes|t.childLanes,c|=Zu,By(e,n,c,y,x,A),e===qe&&(Ae=qe=null,_e=0),ei=t,Zn=e,ti=n,$o=c,Wo=o,vh=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,s0(Tl,function(){return Uh(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=B.T,B.T=null,o=J.p,J.p=2,y=ke,ke|=4;try{$g(e,t,n)}finally{ke=y,J.p=o,B.T=i}}ot=1,Dh(),kh(),Mh()}}function Dh(){if(ot===1){ot=0;var e=Zn,t=ei,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=B.T,B.T=null;var i=J.p;J.p=2;var o=ke;ke|=4;try{ph(t,e);var c=ms,y=Sf(e.containerInfo),x=c.focusedElem,A=c.selectionRange;if(y!==x&&x&&x.ownerDocument&&xf(x.ownerDocument.documentElement,x)){if(A!==null&&Yu(x)){var D=A.start,V=A.end;if(V===void 0&&(V=D),"selectionStart"in x)x.selectionStart=D,x.selectionEnd=Math.min(V,x.value.length);else{var Q=x.ownerDocument||document,k=Q&&Q.defaultView||window;if(k.getSelection){var M=k.getSelection(),he=x.textContent.length,ce=Math.min(A.start,he),Be=A.end===void 0?ce:Math.min(A.end,he);!M.extend&&ce>Be&&(y=Be,Be=ce,ce=y);var O=bf(x,ce),w=bf(x,Be);if(O&&w&&(M.rangeCount!==1||M.anchorNode!==O.node||M.anchorOffset!==O.offset||M.focusNode!==w.node||M.focusOffset!==w.offset)){var R=Q.createRange();R.setStart(O.node,O.offset),M.removeAllRanges(),ce>Be?(M.addRange(R),M.extend(w.node,w.offset)):(R.setEnd(w.node,w.offset),M.addRange(R))}}}}for(Q=[],M=x;M=M.parentNode;)M.nodeType===1&&Q.push({element:M,left:M.scrollLeft,top:M.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<Q.length;x++){var X=Q[x];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}Yr=!!ps,ms=ps=null}finally{ke=o,J.p=i,B.T=n}}e.current=t,ot=2}}function kh(){if(ot===2){ot=0;var e=Zn,t=ei,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=B.T,B.T=null;var i=J.p;J.p=2;var o=ke;ke|=4;try{ch(e,t.alternate,t)}finally{ke=o,J.p=i,B.T=n}}ot=3}}function Mh(){if(ot===4||ot===3){ot=0,Ua();var e=Zn,t=ei,n=ti,i=vh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,ei=Zn=null,jh(e,e.pendingLanes));var o=e.pendingLanes;if(o===0&&(Qn=null),Su(n),t=t.stateNode,Z&&typeof Z.onCommitFiberRoot=="function")try{Z.onCommitFiberRoot(Y,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=B.T,o=J.p,J.p=2,B.T=null;try{for(var c=e.onRecoverableError,y=0;y<i.length;y++){var x=i[y];c(x.value,{componentStack:x.stack})}}finally{B.T=t,J.p=o}}(ti&3)!==0&&Nr(),sn(e),o=e.pendingLanes,(n&4194090)!==0&&(o&42)!==0?e===es?ia++:(ia=0,es=e):ia=0,aa(0)}}function jh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Li(t)))}function Nr(e){return Dh(),kh(),Mh(),Uh()}function Uh(){if(ot!==5)return!1;var e=Zn,t=$o;$o=0;var n=Su(ti),i=B.T,o=J.p;try{J.p=32>n?32:n,B.T=null,n=Wo,Wo=null;var c=Zn,y=ti;if(ot=0,ei=Zn=null,ti=0,(ke&6)!==0)throw Error(u(331));var x=ke;if(ke|=4,xh(c.current),yh(c,c.current,y,n),ke=x,aa(0,!1),Z&&typeof Z.onPostCommitFiberRoot=="function")try{Z.onPostCommitFiberRoot(Y,c)}catch{}return!0}finally{J.p=o,B.T=i,jh(e,t)}}function Bh(e,t,n){t=Xt(n,t),t=Do(e.stateNode,t,2),e=Un(e,t,2),e!==null&&(Ei(e,2),sn(e))}function Le(e,t,n){if(e.tag===3)Bh(e,e,n);else for(;t!==null;){if(t.tag===3){Bh(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Qn===null||!Qn.has(i))){e=Xt(n,e),n=Yd(2),i=Un(t,n,2),i!==null&&(Vd(n,i,t,e),Ei(i,2),sn(i));break}}t=t.return}}function is(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new t0;var o=new Set;i.set(t,o)}else o=i.get(t),o===void 0&&(o=new Set,i.set(t,o));o.has(n)||(Jo=!0,o.add(n),e=r0.bind(null,e,t,n),t.then(e,e))}function r0(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,qe===e&&(_e&n)===n&&(Ke===4||Ke===3&&(_e&62914560)===_e&&300>Ct()-Io?(ke&2)===0&&ni(e,0):Fo|=n,Wl===_e&&(Wl=0)),sn(e)}function Lh(e,t){t===0&&(t=kc()),e=Ll(e,t),e!==null&&(Ei(e,t),sn(e))}function u0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Lh(e,n)}function o0(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(u(314))}i!==null&&i.delete(t),Lh(e,n)}function s0(e,t){return bi(e,t)}var Rr=null,ii=null,as=!1,zr=!1,rs=!1,bl=0;function sn(e){e!==ii&&e.next===null&&(ii===null?Rr=ii=e:ii=ii.next=e),zr=!0,as||(as=!0,f0())}function aa(e,t){if(!rs&&zr){rs=!0;do for(var n=!1,i=Rr;i!==null;){if(e!==0){var o=i.pendingLanes;if(o===0)var c=0;else{var y=i.suspendedLanes,x=i.pingedLanes;c=(1<<31-de(42|e)+1)-1,c&=o&~(y&~x),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Vh(i,c))}else c=_e,c=nt(i,i===qe?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||qt(i,c)||(n=!0,Vh(i,c));i=i.next}while(n);rs=!1}}function c0(){Hh()}function Hh(){zr=as=!1;var e=0;bl!==0&&(x0()&&(e=bl),bl=0);for(var t=Ct(),n=null,i=Rr;i!==null;){var o=i.next,c=qh(i,t);c===0?(i.next=null,n===null?Rr=o:n.next=o,o===null&&(ii=n)):(n=i,(e!==0||(c&3)!==0)&&(zr=!0)),i=o}aa(e)}function qh(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,o=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var y=31-de(c),x=1<<y,A=o[y];A===-1?((x&n)===0||(x&i)!==0)&&(o[y]=It(x,t)):A<=t&&(e.expiredLanes|=x),c&=~x}if(t=qe,n=_e,n=nt(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Me===2||Me===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&xi(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||qt(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&xi(i),Su(n)){case 2:case 8:n=vi;break;case 32:n=Tl;break;case 268435456:n=Ba;break;default:n=Tl}return i=Yh.bind(null,e),n=bi(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&xi(i),e.callbackPriority=2,e.callbackNode=null,2}function Yh(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Nr()&&e.callbackNode!==n)return null;var i=_e;return i=nt(e,e===qe?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(Th(e,i,t),qh(e,Ct()),e.callbackNode!=null&&e.callbackNode===n?Yh.bind(null,e):null)}function Vh(e,t){if(Nr())return null;Th(e,t,!0)}function f0(){v0(function(){(ke&6)!==0?bi(Si,c0):Hh()})}function us(){return bl===0&&(bl=Dc()),bl}function Xh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Va(""+e)}function Gh(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function d0(e,t,n,i,o){if(t==="submit"&&n&&n.stateNode===o){var c=Xh((o[bt]||null).action),y=i.submitter;y&&(t=(t=y[bt]||null)?Xh(t.formAction):y.getAttribute("formAction"),t!==null&&(c=t,y=null));var x=new Za("action","action",null,i,o);e.push({event:x,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(bl!==0){var A=y?Gh(o,y):new FormData(o);_o(n,{pending:!0,data:A,method:o.method,action:c},null,A)}}else typeof c=="function"&&(x.preventDefault(),A=y?Gh(o,y):new FormData(o),_o(n,{pending:!0,data:A,method:o.method,action:c},c,A))},currentTarget:o}]})}}for(var os=0;os<Qu.length;os++){var ss=Qu[os],h0=ss.toLowerCase(),p0=ss[0].toUpperCase()+ss.slice(1);$t(h0,"on"+p0)}$t(Tf,"onAnimationEnd"),$t(Af,"onAnimationIteration"),$t(wf,"onAnimationStart"),$t("dblclick","onDoubleClick"),$t("focusin","onFocus"),$t("focusout","onBlur"),$t(zg,"onTransitionRun"),$t(Dg,"onTransitionStart"),$t(kg,"onTransitionCancel"),$t(Of,"onTransitionEnd"),Cl("onMouseEnter",["mouseout","mouseover"]),Cl("onMouseLeave",["mouseout","mouseover"]),Cl("onPointerEnter",["pointerout","pointerover"]),Cl("onPointerLeave",["pointerout","pointerover"]),nl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),nl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),nl("onBeforeInput",["compositionend","keypress","textInput","paste"]),nl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),nl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),nl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ra="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),m0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ra));function Qh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],o=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var y=i.length-1;0<=y;y--){var x=i[y],A=x.instance,D=x.currentTarget;if(x=x.listener,A!==c&&o.isPropagationStopped())break e;c=x,o.currentTarget=D;try{c(o)}catch(V){br(V)}o.currentTarget=null,c=A}else for(y=0;y<i.length;y++){if(x=i[y],A=x.instance,D=x.currentTarget,x=x.listener,A!==c&&o.isPropagationStopped())break e;c=x,o.currentTarget=D;try{c(o)}catch(V){br(V)}o.currentTarget=null,c=A}}}}function we(e,t){var n=t[vu];n===void 0&&(n=t[vu]=new Set);var i=e+"__bubble";n.has(i)||(Zh(t,e,2,!1),n.add(i))}function cs(e,t,n){var i=0;t&&(i|=4),Zh(n,e,i,t)}var Dr="_reactListening"+Math.random().toString(36).slice(2);function fs(e){if(!e[Dr]){e[Dr]=!0,Lc.forEach(function(n){n!=="selectionchange"&&(m0.has(n)||cs(n,!1,e),cs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Dr]||(t[Dr]=!0,cs("selectionchange",!1,t))}}function Zh(e,t,n,i){switch(yp(t)){case 2:var o=V0;break;case 8:o=X0;break;default:o=ws}n=o.bind(null,t,n,e),o=void 0,!Du||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),i?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function ds(e,t,n,i,o){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var y=i.tag;if(y===3||y===4){var x=i.stateNode.containerInfo;if(x===o)break;if(y===4)for(y=i.return;y!==null;){var A=y.tag;if((A===3||A===4)&&y.stateNode.containerInfo===o)return;y=y.return}for(;x!==null;){if(y=wl(x),y===null)return;if(A=y.tag,A===5||A===6||A===26||A===27){i=c=y;continue e}x=x.parentNode}}i=i.return}$c(function(){var D=c,V=Ru(n),Q=[];e:{var k=_f.get(e);if(k!==void 0){var M=Za,he=e;switch(e){case"keypress":if(Ga(n)===0)break e;case"keydown":case"keyup":M=sg;break;case"focusin":he="focus",M=Uu;break;case"focusout":he="blur",M=Uu;break;case"beforeblur":case"afterblur":M=Uu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=tf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=Iy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=dg;break;case Tf:case Af:case wf:M=eg;break;case Of:M=pg;break;case"scroll":case"scrollend":M=Fy;break;case"wheel":M=yg;break;case"copy":case"cut":case"paste":M=ng;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=lf;break;case"toggle":case"beforetoggle":M=bg}var ce=(t&4)!==0,Be=!ce&&(e==="scroll"||e==="scrollend"),O=ce?k!==null?k+"Capture":null:k;ce=[];for(var w=D,R;w!==null;){var X=w;if(R=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||R===null||O===null||(X=wi(w,O),X!=null&&ce.push(ua(w,X,R))),Be)break;w=w.return}0<ce.length&&(k=new M(k,he,null,n,V),Q.push({event:k,listeners:ce}))}}if((t&7)===0){e:{if(k=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",k&&n!==Nu&&(he=n.relatedTarget||n.fromElement)&&(wl(he)||he[Al]))break e;if((M||k)&&(k=V.window===V?V:(k=V.ownerDocument)?k.defaultView||k.parentWindow:window,M?(he=n.relatedTarget||n.toElement,M=D,he=he?wl(he):null,he!==null&&(Be=f(he),ce=he.tag,he!==Be||ce!==5&&ce!==27&&ce!==6)&&(he=null)):(M=null,he=D),M!==he)){if(ce=tf,X="onMouseLeave",O="onMouseEnter",w="mouse",(e==="pointerout"||e==="pointerover")&&(ce=lf,X="onPointerLeave",O="onPointerEnter",w="pointer"),Be=M==null?k:Ai(M),R=he==null?k:Ai(he),k=new ce(X,w+"leave",M,n,V),k.target=Be,k.relatedTarget=R,X=null,wl(V)===D&&(ce=new ce(O,w+"enter",he,n,V),ce.target=R,ce.relatedTarget=Be,X=ce),Be=X,M&&he)t:{for(ce=M,O=he,w=0,R=ce;R;R=ai(R))w++;for(R=0,X=O;X;X=ai(X))R++;for(;0<w-R;)ce=ai(ce),w--;for(;0<R-w;)O=ai(O),R--;for(;w--;){if(ce===O||O!==null&&ce===O.alternate)break t;ce=ai(ce),O=ai(O)}ce=null}else ce=null;M!==null&&Kh(Q,k,M,ce,!1),he!==null&&Be!==null&&Kh(Q,Be,he,ce,!0)}}e:{if(k=D?Ai(D):window,M=k.nodeName&&k.nodeName.toLowerCase(),M==="select"||M==="input"&&k.type==="file")var ee=df;else if(cf(k))if(hf)ee=Cg;else{ee=Og;var Ee=wg}else M=k.nodeName,!M||M.toLowerCase()!=="input"||k.type!=="checkbox"&&k.type!=="radio"?D&&Cu(D.elementType)&&(ee=df):ee=_g;if(ee&&(ee=ee(e,D))){ff(Q,ee,n,V);break e}Ee&&Ee(e,k,D),e==="focusout"&&D&&k.type==="number"&&D.memoizedProps.value!=null&&_u(k,"number",k.value)}switch(Ee=D?Ai(D):window,e){case"focusin":(cf(Ee)||Ee.contentEditable==="true")&&(jl=Ee,Vu=D,ki=null);break;case"focusout":ki=Vu=jl=null;break;case"mousedown":Xu=!0;break;case"contextmenu":case"mouseup":case"dragend":Xu=!1,vf(Q,n,V);break;case"selectionchange":if(Rg)break;case"keydown":case"keyup":vf(Q,n,V)}var ue;if(Lu)e:{switch(e){case"compositionstart":var fe="onCompositionStart";break e;case"compositionend":fe="onCompositionEnd";break e;case"compositionupdate":fe="onCompositionUpdate";break e}fe=void 0}else Ml?of(e,n)&&(fe="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(fe="onCompositionStart");fe&&(af&&n.locale!=="ko"&&(Ml||fe!=="onCompositionStart"?fe==="onCompositionEnd"&&Ml&&(ue=Wc()):(Dn=V,ku="value"in Dn?Dn.value:Dn.textContent,Ml=!0)),Ee=kr(D,fe),0<Ee.length&&(fe=new nf(fe,e,null,n,V),Q.push({event:fe,listeners:Ee}),ue?fe.data=ue:(ue=sf(n),ue!==null&&(fe.data=ue)))),(ue=Sg?vg(e,n):Eg(e,n))&&(fe=kr(D,"onBeforeInput"),0<fe.length&&(Ee=new nf("onBeforeInput","beforeinput",null,n,V),Q.push({event:Ee,listeners:fe}),Ee.data=ue)),d0(Q,e,D,n,V)}Qh(Q,t)})}function ua(e,t,n){return{instance:e,listener:t,currentTarget:n}}function kr(e,t){for(var n=t+"Capture",i=[];e!==null;){var o=e,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=wi(e,n),o!=null&&i.unshift(ua(e,o,c)),o=wi(e,t),o!=null&&i.push(ua(e,o,c))),e.tag===3)return i;e=e.return}return[]}function ai(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Kh(e,t,n,i,o){for(var c=t._reactName,y=[];n!==null&&n!==i;){var x=n,A=x.alternate,D=x.stateNode;if(x=x.tag,A!==null&&A===i)break;x!==5&&x!==26&&x!==27||D===null||(A=D,o?(D=wi(n,c),D!=null&&y.unshift(ua(n,D,A))):o||(D=wi(n,c),D!=null&&y.push(ua(n,D,A)))),n=n.return}y.length!==0&&e.push({event:t,listeners:y})}var y0=/\r\n?/g,g0=/\u0000|\uFFFD/g;function Jh(e){return(typeof e=="string"?e:""+e).replace(y0,`
`).replace(g0,"")}function Fh(e,t){return t=Jh(t),Jh(e)===t}function Mr(){}function Ue(e,t,n,i,o,c){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||zl(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&zl(e,""+i);break;case"className":Ha(e,"class",i);break;case"tabIndex":Ha(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Ha(e,n,i);break;case"style":Pc(e,i,c);break;case"data":if(t!=="object"){Ha(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=Va(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Ue(e,t,"name",o.name,o,null),Ue(e,t,"formEncType",o.formEncType,o,null),Ue(e,t,"formMethod",o.formMethod,o,null),Ue(e,t,"formTarget",o.formTarget,o,null)):(Ue(e,t,"encType",o.encType,o,null),Ue(e,t,"method",o.method,o,null),Ue(e,t,"target",o.target,o,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=Va(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=Mr);break;case"onScroll":i!=null&&we("scroll",e);break;case"onScrollEnd":i!=null&&we("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(u(61));if(n=i.__html,n!=null){if(o.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=Va(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":we("beforetoggle",e),we("toggle",e),La(e,"popover",i);break;case"xlinkActuate":yn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":yn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":yn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":yn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":yn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":yn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":yn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":yn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":yn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":La(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Ky.get(n)||n,La(e,n,i))}}function hs(e,t,n,i,o,c){switch(n){case"style":Pc(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(u(61));if(n=i.__html,n!=null){if(o.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"children":typeof i=="string"?zl(e,i):(typeof i=="number"||typeof i=="bigint")&&zl(e,""+i);break;case"onScroll":i!=null&&we("scroll",e);break;case"onScrollEnd":i!=null&&we("scrollend",e);break;case"onClick":i!=null&&(e.onclick=Mr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Hc.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),c=e[bt]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,o),typeof i=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,o);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):La(e,n,i)}}}function st(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":we("error",e),we("load",e);var i=!1,o=!1,c;for(c in n)if(n.hasOwnProperty(c)){var y=n[c];if(y!=null)switch(c){case"src":i=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ue(e,t,c,y,n,null)}}o&&Ue(e,t,"srcSet",n.srcSet,n,null),i&&Ue(e,t,"src",n.src,n,null);return;case"input":we("invalid",e);var x=c=y=o=null,A=null,D=null;for(i in n)if(n.hasOwnProperty(i)){var V=n[i];if(V!=null)switch(i){case"name":o=V;break;case"type":y=V;break;case"checked":A=V;break;case"defaultChecked":D=V;break;case"value":c=V;break;case"defaultValue":x=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(u(137,t));break;default:Ue(e,t,i,V,n,null)}}Zc(e,c,x,A,D,y,o,!1),qa(e);return;case"select":we("invalid",e),i=y=c=null;for(o in n)if(n.hasOwnProperty(o)&&(x=n[o],x!=null))switch(o){case"value":c=x;break;case"defaultValue":y=x;break;case"multiple":i=x;default:Ue(e,t,o,x,n,null)}t=c,n=y,e.multiple=!!i,t!=null?Rl(e,!!i,t,!1):n!=null&&Rl(e,!!i,n,!0);return;case"textarea":we("invalid",e),c=o=i=null;for(y in n)if(n.hasOwnProperty(y)&&(x=n[y],x!=null))switch(y){case"value":i=x;break;case"defaultValue":o=x;break;case"children":c=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(u(91));break;default:Ue(e,t,y,x,n,null)}Jc(e,i,o,c),qa(e);return;case"option":for(A in n)if(n.hasOwnProperty(A)&&(i=n[A],i!=null))switch(A){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Ue(e,t,A,i,n,null)}return;case"dialog":we("beforetoggle",e),we("toggle",e),we("cancel",e),we("close",e);break;case"iframe":case"object":we("load",e);break;case"video":case"audio":for(i=0;i<ra.length;i++)we(ra[i],e);break;case"image":we("error",e),we("load",e);break;case"details":we("toggle",e);break;case"embed":case"source":case"link":we("error",e),we("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(i=n[D],i!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ue(e,t,D,i,n,null)}return;default:if(Cu(t)){for(V in n)n.hasOwnProperty(V)&&(i=n[V],i!==void 0&&hs(e,t,V,i,n,void 0));return}}for(x in n)n.hasOwnProperty(x)&&(i=n[x],i!=null&&Ue(e,t,x,i,n,null))}function b0(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,y=null,x=null,A=null,D=null,V=null;for(M in n){var Q=n[M];if(n.hasOwnProperty(M)&&Q!=null)switch(M){case"checked":break;case"value":break;case"defaultValue":A=Q;default:i.hasOwnProperty(M)||Ue(e,t,M,null,i,Q)}}for(var k in i){var M=i[k];if(Q=n[k],i.hasOwnProperty(k)&&(M!=null||Q!=null))switch(k){case"type":c=M;break;case"name":o=M;break;case"checked":D=M;break;case"defaultChecked":V=M;break;case"value":y=M;break;case"defaultValue":x=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(u(137,t));break;default:M!==Q&&Ue(e,t,k,M,i,Q)}}Ou(e,y,x,A,D,V,c,o);return;case"select":M=y=x=k=null;for(c in n)if(A=n[c],n.hasOwnProperty(c)&&A!=null)switch(c){case"value":break;case"multiple":M=A;default:i.hasOwnProperty(c)||Ue(e,t,c,null,i,A)}for(o in i)if(c=i[o],A=n[o],i.hasOwnProperty(o)&&(c!=null||A!=null))switch(o){case"value":k=c;break;case"defaultValue":x=c;break;case"multiple":y=c;default:c!==A&&Ue(e,t,o,c,i,A)}t=x,n=y,i=M,k!=null?Rl(e,!!n,k,!1):!!i!=!!n&&(t!=null?Rl(e,!!n,t,!0):Rl(e,!!n,n?[]:"",!1));return;case"textarea":M=k=null;for(x in n)if(o=n[x],n.hasOwnProperty(x)&&o!=null&&!i.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Ue(e,t,x,null,i,o)}for(y in i)if(o=i[y],c=n[y],i.hasOwnProperty(y)&&(o!=null||c!=null))switch(y){case"value":k=o;break;case"defaultValue":M=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(u(91));break;default:o!==c&&Ue(e,t,y,o,i,c)}Kc(e,k,M);return;case"option":for(var he in n)if(k=n[he],n.hasOwnProperty(he)&&k!=null&&!i.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:Ue(e,t,he,null,i,k)}for(A in i)if(k=i[A],M=n[A],i.hasOwnProperty(A)&&k!==M&&(k!=null||M!=null))switch(A){case"selected":e.selected=k&&typeof k!="function"&&typeof k!="symbol";break;default:Ue(e,t,A,k,i,M)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ce in n)k=n[ce],n.hasOwnProperty(ce)&&k!=null&&!i.hasOwnProperty(ce)&&Ue(e,t,ce,null,i,k);for(D in i)if(k=i[D],M=n[D],i.hasOwnProperty(D)&&k!==M&&(k!=null||M!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(u(137,t));break;default:Ue(e,t,D,k,i,M)}return;default:if(Cu(t)){for(var Be in n)k=n[Be],n.hasOwnProperty(Be)&&k!==void 0&&!i.hasOwnProperty(Be)&&hs(e,t,Be,void 0,i,k);for(V in i)k=i[V],M=n[V],!i.hasOwnProperty(V)||k===M||k===void 0&&M===void 0||hs(e,t,V,k,i,M);return}}for(var O in n)k=n[O],n.hasOwnProperty(O)&&k!=null&&!i.hasOwnProperty(O)&&Ue(e,t,O,null,i,k);for(Q in i)k=i[Q],M=n[Q],!i.hasOwnProperty(Q)||k===M||k==null&&M==null||Ue(e,t,Q,k,i,M)}var ps=null,ms=null;function jr(e){return e.nodeType===9?e:e.ownerDocument}function Ph(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ih(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function ys(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var gs=null;function x0(){var e=window.event;return e&&e.type==="popstate"?e===gs?!1:(gs=e,!0):(gs=null,!1)}var $h=typeof setTimeout=="function"?setTimeout:void 0,S0=typeof clearTimeout=="function"?clearTimeout:void 0,Wh=typeof Promise=="function"?Promise:void 0,v0=typeof queueMicrotask=="function"?queueMicrotask:typeof Wh<"u"?function(e){return Wh.resolve(null).then(e).catch(E0)}:$h;function E0(e){setTimeout(function(){throw e})}function Jn(e){return e==="head"}function ep(e,t){var n=t,i=0,o=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<i&&8>i){n=i;var y=e.ownerDocument;if(n&1&&oa(y.documentElement),n&2&&oa(y.body),n&4)for(n=y.head,oa(n),y=n.firstChild;y;){var x=y.nextSibling,A=y.nodeName;y[Ti]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&y.rel.toLowerCase()==="stylesheet"||n.removeChild(y),y=x}}if(o===0){e.removeChild(c),ya(t);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:i=n.charCodeAt(0)-48;else i=0;n=c}while(n);ya(t)}function bs(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":bs(n),Eu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function T0(e,t,n,i){for(;e.nodeType===1;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Ti])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==o.rel||e.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||e.getAttribute("title")!==(o.title==null?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(o.src==null?null:o.src)||e.getAttribute("type")!==(o.type==null?null:o.type)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=en(e.nextSibling),e===null)break}return null}function A0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=en(e.nextSibling),e===null))return null;return e}function xs(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function w0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function en(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ss=null;function tp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function np(e,t,n){switch(t=jr(n),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function oa(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Eu(e)}var Ft=new Map,lp=new Set;function Ur(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Rn=J.d;J.d={f:O0,r:_0,D:C0,C:N0,L:R0,m:z0,X:k0,S:D0,M:M0};function O0(){var e=Rn.f(),t=_r();return e||t}function _0(e){var t=Ol(e);t!==null&&t.tag===5&&t.type==="form"?Td(t):Rn.r(e)}var ri=typeof document>"u"?null:document;function ip(e,t,n){var i=ri;if(i&&typeof t=="string"&&t){var o=Vt(t);o='link[rel="'+e+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),lp.has(o)||(lp.add(o),e={rel:e,crossOrigin:n,href:t},i.querySelector(o)===null&&(t=i.createElement("link"),st(t,"link",e),lt(t),i.head.appendChild(t)))}}function C0(e){Rn.D(e),ip("dns-prefetch",e,null)}function N0(e,t){Rn.C(e,t),ip("preconnect",e,t)}function R0(e,t,n){Rn.L(e,t,n);var i=ri;if(i&&e&&t){var o='link[rel="preload"][as="'+Vt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+Vt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+Vt(n.imageSizes)+'"]')):o+='[href="'+Vt(e)+'"]';var c=o;switch(t){case"style":c=ui(e);break;case"script":c=oi(e)}Ft.has(c)||(e=m({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Ft.set(c,e),i.querySelector(o)!==null||t==="style"&&i.querySelector(sa(c))||t==="script"&&i.querySelector(ca(c))||(t=i.createElement("link"),st(t,"link",e),lt(t),i.head.appendChild(t)))}}function z0(e,t){Rn.m(e,t);var n=ri;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",o='link[rel="modulepreload"][as="'+Vt(i)+'"][href="'+Vt(e)+'"]',c=o;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=oi(e)}if(!Ft.has(c)&&(e=m({rel:"modulepreload",href:e},t),Ft.set(c,e),n.querySelector(o)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ca(c)))return}i=n.createElement("link"),st(i,"link",e),lt(i),n.head.appendChild(i)}}}function D0(e,t,n){Rn.S(e,t,n);var i=ri;if(i&&e){var o=_l(i).hoistableStyles,c=ui(e);t=t||"default";var y=o.get(c);if(!y){var x={loading:0,preload:null};if(y=i.querySelector(sa(c)))x.loading=5;else{e=m({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Ft.get(c))&&vs(e,n);var A=y=i.createElement("link");lt(A),st(A,"link",e),A._p=new Promise(function(D,V){A.onload=D,A.onerror=V}),A.addEventListener("load",function(){x.loading|=1}),A.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Br(y,t,i)}y={type:"stylesheet",instance:y,count:1,state:x},o.set(c,y)}}}function k0(e,t){Rn.X(e,t);var n=ri;if(n&&e){var i=_l(n).hoistableScripts,o=oi(e),c=i.get(o);c||(c=n.querySelector(ca(o)),c||(e=m({src:e,async:!0},t),(t=Ft.get(o))&&Es(e,t),c=n.createElement("script"),lt(c),st(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(o,c))}}function M0(e,t){Rn.M(e,t);var n=ri;if(n&&e){var i=_l(n).hoistableScripts,o=oi(e),c=i.get(o);c||(c=n.querySelector(ca(o)),c||(e=m({src:e,async:!0,type:"module"},t),(t=Ft.get(o))&&Es(e,t),c=n.createElement("script"),lt(c),st(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(o,c))}}function ap(e,t,n,i){var o=(o=le.current)?Ur(o):null;if(!o)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ui(n.href),n=_l(o).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ui(n.href);var c=_l(o).hoistableStyles,y=c.get(e);if(y||(o=o.ownerDocument||o,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,y),(c=o.querySelector(sa(e)))&&!c._p&&(y.instance=c,y.state.loading=5),Ft.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ft.set(e,n),c||j0(o,e,n,y.state))),t&&i===null)throw Error(u(528,""));return y}if(t&&i!==null)throw Error(u(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=oi(n),n=_l(o).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function ui(e){return'href="'+Vt(e)+'"'}function sa(e){return'link[rel="stylesheet"]['+e+"]"}function rp(e){return m({},e,{"data-precedence":e.precedence,precedence:null})}function j0(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),st(t,"link",n),lt(t),e.head.appendChild(t))}function oi(e){return'[src="'+Vt(e)+'"]'}function ca(e){return"script[async]"+e}function up(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+Vt(n.href)+'"]');if(i)return t.instance=i,lt(i),i;var o=m({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),lt(i),st(i,"style",o),Br(i,n.precedence,e),t.instance=i;case"stylesheet":o=ui(n.href);var c=e.querySelector(sa(o));if(c)return t.state.loading|=4,t.instance=c,lt(c),c;i=rp(n),(o=Ft.get(o))&&vs(i,o),c=(e.ownerDocument||e).createElement("link"),lt(c);var y=c;return y._p=new Promise(function(x,A){y.onload=x,y.onerror=A}),st(c,"link",i),t.state.loading|=4,Br(c,n.precedence,e),t.instance=c;case"script":return c=oi(n.src),(o=e.querySelector(ca(c)))?(t.instance=o,lt(o),o):(i=n,(o=Ft.get(c))&&(i=m({},n),Es(i,o)),e=e.ownerDocument||e,o=e.createElement("script"),lt(o),st(o,"link",i),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,Br(i,n.precedence,e));return t.instance}function Br(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=i.length?i[i.length-1]:null,c=o,y=0;y<i.length;y++){var x=i[y];if(x.dataset.precedence===t)c=x;else if(c!==o)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function vs(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Es(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Lr=null;function op(e,t,n){if(Lr===null){var i=new Map,o=Lr=new Map;o.set(n,i)}else o=Lr,i=o.get(n),i||(i=new Map,o.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var c=n[o];if(!(c[Ti]||c[ct]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var y=c.getAttribute(t)||"";y=e+y;var x=i.get(y);x?x.push(c):i.set(y,[c])}}return i}function sp(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function U0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function cp(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var fa=null;function B0(){}function L0(e,t,n){if(fa===null)throw Error(u(475));var i=fa;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var o=ui(n.href),c=e.querySelector(sa(o));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=Hr.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,lt(c);return}c=e.ownerDocument||e,n=rp(n),(o=Ft.get(o))&&vs(n,o),c=c.createElement("link"),lt(c);var y=c;y._p=new Promise(function(x,A){y.onload=x,y.onerror=A}),st(c,"link",n),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=Hr.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function H0(){if(fa===null)throw Error(u(475));var e=fa;return e.stylesheets&&e.count===0&&Ts(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Ts(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Hr(){if(this.count--,this.count===0){if(this.stylesheets)Ts(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var qr=null;function Ts(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,qr=new Map,t.forEach(q0,e),qr=null,Hr.call(e))}function q0(e,t){if(!(t.state.loading&4)){var n=qr.get(e);if(n)var i=n.get(null);else{n=new Map,qr.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var y=o[c];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(n.set(y.dataset.precedence,y),i=y)}i&&n.set(null,i)}o=t.instance,y=o.getAttribute("data-precedence"),c=n.get(y)||i,c===i&&n.set(null,o),n.set(y,o),this.count++,i=Hr.bind(this),o.addEventListener("load",i),o.addEventListener("error",i),c?c.parentNode.insertBefore(o,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(o,e.firstChild)),t.state.loading|=4}}var da={$$typeof:H,Provider:null,Consumer:null,_currentValue:ae,_currentValue2:ae,_threadCount:0};function Y0(e,t,n,i,o,c,y,x){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=bu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=bu(0),this.hiddenUpdates=bu(null),this.identifierPrefix=i,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function fp(e,t,n,i,o,c,y,x,A,D,V,Q){return e=new Y0(e,t,n,y,x,A,D,Q),t=1,c===!0&&(t|=24),c=Dt(3,null,null,t),e.current=c,c.stateNode=e,t=lo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:n,cache:t},uo(c),e}function dp(e){return e?(e=Hl,e):Hl}function hp(e,t,n,i,o,c){o=dp(o),i.context===null?i.context=o:i.pendingContext=o,i=jn(t),i.payload={element:n},c=c===void 0?null:c,c!==null&&(i.callback=c),n=Un(e,i,t),n!==null&&(Bt(n,e,t),Vi(n,e,t))}function pp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function As(e,t){pp(e,t),(e=e.alternate)&&pp(e,t)}function mp(e){if(e.tag===13){var t=Ll(e,67108864);t!==null&&Bt(t,e,67108864),As(e,67108864)}}var Yr=!0;function V0(e,t,n,i){var o=B.T;B.T=null;var c=J.p;try{J.p=2,ws(e,t,n,i)}finally{J.p=c,B.T=o}}function X0(e,t,n,i){var o=B.T;B.T=null;var c=J.p;try{J.p=8,ws(e,t,n,i)}finally{J.p=c,B.T=o}}function ws(e,t,n,i){if(Yr){var o=Os(i);if(o===null)ds(e,t,i,Vr,n),gp(e,i);else if(Q0(o,e,t,n,i))i.stopPropagation();else if(gp(e,i),t&4&&-1<G0.indexOf(e)){for(;o!==null;){var c=Ol(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var y=Rt(c.pendingLanes);if(y!==0){var x=c;for(x.pendingLanes|=2,x.entangledLanes|=2;y;){var A=1<<31-de(y);x.entanglements[1]|=A,y&=~A}sn(c),(ke&6)===0&&(wr=Ct()+500,aa(0))}}break;case 13:x=Ll(c,2),x!==null&&Bt(x,c,2),_r(),As(c,2)}if(c=Os(i),c===null&&ds(e,t,i,Vr,n),c===o)break;o=c}o!==null&&i.stopPropagation()}else ds(e,t,i,null,n)}}function Os(e){return e=Ru(e),_s(e)}var Vr=null;function _s(e){if(Vr=null,e=wl(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Vr=e,null}function yp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(pu()){case Si:return 2;case vi:return 8;case Tl:case mu:return 32;case Ba:return 268435456;default:return 32}default:return 32}}var Cs=!1,Fn=null,Pn=null,In=null,ha=new Map,pa=new Map,$n=[],G0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function gp(e,t){switch(e){case"focusin":case"focusout":Fn=null;break;case"dragenter":case"dragleave":Pn=null;break;case"mouseover":case"mouseout":In=null;break;case"pointerover":case"pointerout":ha.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":pa.delete(t.pointerId)}}function ma(e,t,n,i,o,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:c,targetContainers:[o]},t!==null&&(t=Ol(t),t!==null&&mp(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Q0(e,t,n,i,o){switch(t){case"focusin":return Fn=ma(Fn,e,t,n,i,o),!0;case"dragenter":return Pn=ma(Pn,e,t,n,i,o),!0;case"mouseover":return In=ma(In,e,t,n,i,o),!0;case"pointerover":var c=o.pointerId;return ha.set(c,ma(ha.get(c)||null,e,t,n,i,o)),!0;case"gotpointercapture":return c=o.pointerId,pa.set(c,ma(pa.get(c)||null,e,t,n,i,o)),!0}return!1}function bp(e){var t=wl(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Ly(e.priority,function(){if(n.tag===13){var i=Ut();i=xu(i);var o=Ll(n,i);o!==null&&Bt(o,n,i),As(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Os(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);Nu=i,n.target.dispatchEvent(i),Nu=null}else return t=Ol(n),t!==null&&mp(t),e.blockedOn=n,!1;t.shift()}return!0}function xp(e,t,n){Xr(e)&&n.delete(t)}function Z0(){Cs=!1,Fn!==null&&Xr(Fn)&&(Fn=null),Pn!==null&&Xr(Pn)&&(Pn=null),In!==null&&Xr(In)&&(In=null),ha.forEach(xp),pa.forEach(xp)}function Gr(e,t){e.blockedOn===t&&(e.blockedOn=null,Cs||(Cs=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,Z0)))}var Qr=null;function Sp(e){Qr!==e&&(Qr=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){Qr===e&&(Qr=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],o=e[t+2];if(typeof i!="function"){if(_s(i||n)===null)continue;break}var c=Ol(n);c!==null&&(e.splice(t,3),t-=3,_o(c,{pending:!0,data:o,method:n.method,action:i},i,o))}}))}function ya(e){function t(A){return Gr(A,e)}Fn!==null&&Gr(Fn,e),Pn!==null&&Gr(Pn,e),In!==null&&Gr(In,e),ha.forEach(t),pa.forEach(t);for(var n=0;n<$n.length;n++){var i=$n[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<$n.length&&(n=$n[0],n.blockedOn===null);)bp(n),n.blockedOn===null&&$n.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var o=n[i],c=n[i+1],y=o[bt]||null;if(typeof c=="function")y||Sp(n);else if(y){var x=null;if(c&&c.hasAttribute("formAction")){if(o=c,y=c[bt]||null)x=y.formAction;else if(_s(o)!==null)continue}else x=y.action;typeof x=="function"?n[i+1]=x:(n.splice(i,3),i-=3),Sp(n)}}}function Ns(e){this._internalRoot=e}Zr.prototype.render=Ns.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var n=t.current,i=Ut();hp(n,i,e,t,null,null)},Zr.prototype.unmount=Ns.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;hp(e.current,2,null,e,null,null),_r(),t[Al]=null}};function Zr(e){this._internalRoot=e}Zr.prototype.unstable_scheduleHydration=function(e){if(e){var t=Uc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$n.length&&t!==0&&t<$n[n].priority;n++);$n.splice(n,0,e),n===0&&bp(e)}};var vp=a.version;if(vp!=="19.1.1")throw Error(u(527,vp,"19.1.1"));J.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=b(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var K0={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Kr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Kr.isDisabled&&Kr.supportsFiber)try{Y=Kr.inject(K0),Z=Kr}catch{}}return ba.createRoot=function(e,t){if(!s(e))throw Error(u(299));var n=!1,i="",o=Bd,c=Ld,y=Hd,x=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(o=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(y=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(x=t.unstable_transitionCallbacks)),t=fp(e,1,!1,null,null,n,i,o,c,y,x,null),e[Al]=t.current,fs(e),new Ns(t)},ba.hydrateRoot=function(e,t,n){if(!s(e))throw Error(u(299));var i=!1,o="",c=Bd,y=Ld,x=Hd,A=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(y=n.onCaughtError),n.onRecoverableError!==void 0&&(x=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(A=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),t=fp(e,1,!0,t,n??null,i,o,c,y,x,A,D),t.context=dp(null),n=t.current,i=Ut(),i=xu(i),o=jn(i),o.callback=null,Un(n,o,i),n=i,t.current.lanes=n,Ei(t,n),sn(t),e[Al]=t.current,fs(e),new Zr(t)},ba.version="19.1.1",ba}var zp;function lb(){if(zp)return Ds.exports;zp=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(a){console.error(a)}}return l(),Ds.exports=nb(),Ds.exports}var ib=lb();function zm(l,a){return function(){return l.apply(a,arguments)}}const{toString:ab}=Object.prototype,{getPrototypeOf:mc}=Object,{iterator:au,toStringTag:Dm}=Symbol,ru=(l=>a=>{const r=ab.call(a);return l[r]||(l[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),tn=l=>(l=l.toLowerCase(),a=>ru(a)===l),uu=l=>a=>typeof a===l,{isArray:hi}=Array,_a=uu("undefined");function Na(l){return l!==null&&!_a(l)&&l.constructor!==null&&!_a(l.constructor)&&At(l.constructor.isBuffer)&&l.constructor.isBuffer(l)}const km=tn("ArrayBuffer");function rb(l){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(l):a=l&&l.buffer&&km(l.buffer),a}const ub=uu("string"),At=uu("function"),Mm=uu("number"),Ra=l=>l!==null&&typeof l=="object",ob=l=>l===!0||l===!1,Ir=l=>{if(ru(l)!=="object")return!1;const a=mc(l);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Dm in l)&&!(au in l)},sb=l=>{if(!Ra(l)||Na(l))return!1;try{return Object.keys(l).length===0&&Object.getPrototypeOf(l)===Object.prototype}catch{return!1}},cb=tn("Date"),fb=tn("File"),db=tn("Blob"),hb=tn("FileList"),pb=l=>Ra(l)&&At(l.pipe),mb=l=>{let a;return l&&(typeof FormData=="function"&&l instanceof FormData||At(l.append)&&((a=ru(l))==="formdata"||a==="object"&&At(l.toString)&&l.toString()==="[object FormData]"))},yb=tn("URLSearchParams"),[gb,bb,xb,Sb]=["ReadableStream","Request","Response","Headers"].map(tn),vb=l=>l.trim?l.trim():l.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function za(l,a,{allOwnKeys:r=!1}={}){if(l===null||typeof l>"u")return;let u,s;if(typeof l!="object"&&(l=[l]),hi(l))for(u=0,s=l.length;u<s;u++)a.call(null,l[u],u,l);else{if(Na(l))return;const f=r?Object.getOwnPropertyNames(l):Object.keys(l),d=f.length;let h;for(u=0;u<d;u++)h=f[u],a.call(null,l[h],h,l)}}function jm(l,a){if(Na(l))return null;a=a.toLowerCase();const r=Object.keys(l);let u=r.length,s;for(;u-- >0;)if(s=r[u],a===s.toLowerCase())return s;return null}const xl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Um=l=>!_a(l)&&l!==xl;function $s(){const{caseless:l}=Um(this)&&this||{},a={},r=(u,s)=>{const f=l&&jm(a,s)||s;Ir(a[f])&&Ir(u)?a[f]=$s(a[f],u):Ir(u)?a[f]=$s({},u):hi(u)?a[f]=u.slice():a[f]=u};for(let u=0,s=arguments.length;u<s;u++)arguments[u]&&za(arguments[u],r);return a}const Eb=(l,a,r,{allOwnKeys:u}={})=>(za(a,(s,f)=>{r&&At(s)?l[f]=zm(s,r):l[f]=s},{allOwnKeys:u}),l),Tb=l=>(l.charCodeAt(0)===65279&&(l=l.slice(1)),l),Ab=(l,a,r,u)=>{l.prototype=Object.create(a.prototype,u),l.prototype.constructor=l,Object.defineProperty(l,"super",{value:a.prototype}),r&&Object.assign(l.prototype,r)},wb=(l,a,r,u)=>{let s,f,d;const h={};if(a=a||{},l==null)return a;do{for(s=Object.getOwnPropertyNames(l),f=s.length;f-- >0;)d=s[f],(!u||u(d,l,a))&&!h[d]&&(a[d]=l[d],h[d]=!0);l=r!==!1&&mc(l)}while(l&&(!r||r(l,a))&&l!==Object.prototype);return a},Ob=(l,a,r)=>{l=String(l),(r===void 0||r>l.length)&&(r=l.length),r-=a.length;const u=l.indexOf(a,r);return u!==-1&&u===r},_b=l=>{if(!l)return null;if(hi(l))return l;let a=l.length;if(!Mm(a))return null;const r=new Array(a);for(;a-- >0;)r[a]=l[a];return r},Cb=(l=>a=>l&&a instanceof l)(typeof Uint8Array<"u"&&mc(Uint8Array)),Nb=(l,a)=>{const u=(l&&l[au]).call(l);let s;for(;(s=u.next())&&!s.done;){const f=s.value;a.call(l,f[0],f[1])}},Rb=(l,a)=>{let r;const u=[];for(;(r=l.exec(a))!==null;)u.push(r);return u},zb=tn("HTMLFormElement"),Db=l=>l.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,u,s){return u.toUpperCase()+s}),Dp=(({hasOwnProperty:l})=>(a,r)=>l.call(a,r))(Object.prototype),kb=tn("RegExp"),Bm=(l,a)=>{const r=Object.getOwnPropertyDescriptors(l),u={};za(r,(s,f)=>{let d;(d=a(s,f,l))!==!1&&(u[f]=d||s)}),Object.defineProperties(l,u)},Mb=l=>{Bm(l,(a,r)=>{if(At(l)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const u=l[r];if(At(u)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},jb=(l,a)=>{const r={},u=s=>{s.forEach(f=>{r[f]=!0})};return hi(l)?u(l):u(String(l).split(a)),r},Ub=()=>{},Bb=(l,a)=>l!=null&&Number.isFinite(l=+l)?l:a;function Lb(l){return!!(l&&At(l.append)&&l[Dm]==="FormData"&&l[au])}const Hb=l=>{const a=new Array(10),r=(u,s)=>{if(Ra(u)){if(a.indexOf(u)>=0)return;if(Na(u))return u;if(!("toJSON"in u)){a[s]=u;const f=hi(u)?[]:{};return za(u,(d,h)=>{const b=r(d,s+1);!_a(b)&&(f[h]=b)}),a[s]=void 0,f}}return u};return r(l,0)},qb=tn("AsyncFunction"),Yb=l=>l&&(Ra(l)||At(l))&&At(l.then)&&At(l.catch),Lm=((l,a)=>l?setImmediate:a?((r,u)=>(xl.addEventListener("message",({source:s,data:f})=>{s===xl&&f===r&&u.length&&u.shift()()},!1),s=>{u.push(s),xl.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",At(xl.postMessage)),Vb=typeof queueMicrotask<"u"?queueMicrotask.bind(xl):typeof process<"u"&&process.nextTick||Lm,Xb=l=>l!=null&&At(l[au]),L={isArray:hi,isArrayBuffer:km,isBuffer:Na,isFormData:mb,isArrayBufferView:rb,isString:ub,isNumber:Mm,isBoolean:ob,isObject:Ra,isPlainObject:Ir,isEmptyObject:sb,isReadableStream:gb,isRequest:bb,isResponse:xb,isHeaders:Sb,isUndefined:_a,isDate:cb,isFile:fb,isBlob:db,isRegExp:kb,isFunction:At,isStream:pb,isURLSearchParams:yb,isTypedArray:Cb,isFileList:hb,forEach:za,merge:$s,extend:Eb,trim:vb,stripBOM:Tb,inherits:Ab,toFlatObject:wb,kindOf:ru,kindOfTest:tn,endsWith:Ob,toArray:_b,forEachEntry:Nb,matchAll:Rb,isHTMLForm:zb,hasOwnProperty:Dp,hasOwnProp:Dp,reduceDescriptors:Bm,freezeMethods:Mb,toObjectSet:jb,toCamelCase:Db,noop:Ub,toFiniteNumber:Bb,findKey:jm,global:xl,isContextDefined:Um,isSpecCompliantForm:Lb,toJSONObject:Hb,isAsyncFn:qb,isThenable:Yb,setImmediate:Lm,asap:Vb,isIterable:Xb};function ge(l,a,r,u,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=l,this.name="AxiosError",a&&(this.code=a),r&&(this.config=r),u&&(this.request=u),s&&(this.response=s,this.status=s.status?s.status:null)}L.inherits(ge,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:L.toJSONObject(this.config),code:this.code,status:this.status}}});const Hm=ge.prototype,qm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(l=>{qm[l]={value:l}});Object.defineProperties(ge,qm);Object.defineProperty(Hm,"isAxiosError",{value:!0});ge.from=(l,a,r,u,s,f)=>{const d=Object.create(Hm);return L.toFlatObject(l,d,function(b){return b!==Error.prototype},h=>h!=="isAxiosError"),ge.call(d,l.message,a,r,u,s),d.cause=l,d.name=l.name,f&&Object.assign(d,f),d};const Gb=null;function Ws(l){return L.isPlainObject(l)||L.isArray(l)}function Ym(l){return L.endsWith(l,"[]")?l.slice(0,-2):l}function kp(l,a,r){return l?l.concat(a).map(function(s,f){return s=Ym(s),!r&&f?"["+s+"]":s}).join(r?".":""):a}function Qb(l){return L.isArray(l)&&!l.some(Ws)}const Zb=L.toFlatObject(L,{},null,function(a){return/^is[A-Z]/.test(a)});function ou(l,a,r){if(!L.isObject(l))throw new TypeError("target must be an object");a=a||new FormData,r=L.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(U,C){return!L.isUndefined(C[U])});const u=r.metaTokens,s=r.visitor||m,f=r.dots,d=r.indexes,b=(r.Blob||typeof Blob<"u"&&Blob)&&L.isSpecCompliantForm(a);if(!L.isFunction(s))throw new TypeError("visitor must be a function");function p(_){if(_===null)return"";if(L.isDate(_))return _.toISOString();if(L.isBoolean(_))return _.toString();if(!b&&L.isBlob(_))throw new ge("Blob is not supported. Use a Buffer instead.");return L.isArrayBuffer(_)||L.isTypedArray(_)?b&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function m(_,U,C){let N=_;if(_&&!C&&typeof _=="object"){if(L.endsWith(U,"{}"))U=u?U:U.slice(0,-2),_=JSON.stringify(_);else if(L.isArray(_)&&Qb(_)||(L.isFileList(_)||L.endsWith(U,"[]"))&&(N=L.toArray(_)))return U=Ym(U),N.forEach(function(H,P){!(L.isUndefined(H)||H===null)&&a.append(d===!0?kp([U],P,f):d===null?U:U+"[]",p(H))}),!1}return Ws(_)?!0:(a.append(kp(C,U,f),p(_)),!1)}const S=[],T=Object.assign(Zb,{defaultVisitor:m,convertValue:p,isVisitable:Ws});function v(_,U){if(!L.isUndefined(_)){if(S.indexOf(_)!==-1)throw Error("Circular reference detected in "+U.join("."));S.push(_),L.forEach(_,function(N,j){(!(L.isUndefined(N)||N===null)&&s.call(a,N,L.isString(j)?j.trim():j,U,T))===!0&&v(N,U?U.concat(j):[j])}),S.pop()}}if(!L.isObject(l))throw new TypeError("data must be an object");return v(l),a}function Mp(l){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(l).replace(/[!'()~]|%20|%00/g,function(u){return a[u]})}function yc(l,a){this._pairs=[],l&&ou(l,this,a)}const Vm=yc.prototype;Vm.append=function(a,r){this._pairs.push([a,r])};Vm.toString=function(a){const r=a?function(u){return a.call(this,u,Mp)}:Mp;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function Kb(l){return encodeURIComponent(l).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Xm(l,a,r){if(!a)return l;const u=r&&r.encode||Kb;L.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let f;if(s?f=s(a,r):f=L.isURLSearchParams(a)?a.toString():new yc(a,r).toString(u),f){const d=l.indexOf("#");d!==-1&&(l=l.slice(0,d)),l+=(l.indexOf("?")===-1?"?":"&")+f}return l}class jp{constructor(){this.handlers=[]}use(a,r,u){return this.handlers.push({fulfilled:a,rejected:r,synchronous:u?u.synchronous:!1,runWhen:u?u.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){L.forEach(this.handlers,function(u){u!==null&&a(u)})}}const Gm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Jb=typeof URLSearchParams<"u"?URLSearchParams:yc,Fb=typeof FormData<"u"?FormData:null,Pb=typeof Blob<"u"?Blob:null,Ib={isBrowser:!0,classes:{URLSearchParams:Jb,FormData:Fb,Blob:Pb},protocols:["http","https","file","blob","url","data"]},gc=typeof window<"u"&&typeof document<"u",ec=typeof navigator=="object"&&navigator||void 0,$b=gc&&(!ec||["ReactNative","NativeScript","NS"].indexOf(ec.product)<0),Wb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",e1=gc&&window.location.href||"http://localhost",t1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:gc,hasStandardBrowserEnv:$b,hasStandardBrowserWebWorkerEnv:Wb,navigator:ec,origin:e1},Symbol.toStringTag,{value:"Module"})),ht={...t1,...Ib};function n1(l,a){return ou(l,new ht.classes.URLSearchParams,{visitor:function(r,u,s,f){return ht.isNode&&L.isBuffer(r)?(this.append(u,r.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)},...a})}function l1(l){return L.matchAll(/\w+|\[(\w*)]/g,l).map(a=>a[0]==="[]"?"":a[1]||a[0])}function i1(l){const a={},r=Object.keys(l);let u;const s=r.length;let f;for(u=0;u<s;u++)f=r[u],a[f]=l[f];return a}function Qm(l){function a(r,u,s,f){let d=r[f++];if(d==="__proto__")return!0;const h=Number.isFinite(+d),b=f>=r.length;return d=!d&&L.isArray(s)?s.length:d,b?(L.hasOwnProp(s,d)?s[d]=[s[d],u]:s[d]=u,!h):((!s[d]||!L.isObject(s[d]))&&(s[d]=[]),a(r,u,s[d],f)&&L.isArray(s[d])&&(s[d]=i1(s[d])),!h)}if(L.isFormData(l)&&L.isFunction(l.entries)){const r={};return L.forEachEntry(l,(u,s)=>{a(l1(u),s,r,0)}),r}return null}function a1(l,a,r){if(L.isString(l))try{return(a||JSON.parse)(l),L.trim(l)}catch(u){if(u.name!=="SyntaxError")throw u}return(r||JSON.stringify)(l)}const Da={transitional:Gm,adapter:["xhr","http","fetch"],transformRequest:[function(a,r){const u=r.getContentType()||"",s=u.indexOf("application/json")>-1,f=L.isObject(a);if(f&&L.isHTMLForm(a)&&(a=new FormData(a)),L.isFormData(a))return s?JSON.stringify(Qm(a)):a;if(L.isArrayBuffer(a)||L.isBuffer(a)||L.isStream(a)||L.isFile(a)||L.isBlob(a)||L.isReadableStream(a))return a;if(L.isArrayBufferView(a))return a.buffer;if(L.isURLSearchParams(a))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let h;if(f){if(u.indexOf("application/x-www-form-urlencoded")>-1)return n1(a,this.formSerializer).toString();if((h=L.isFileList(a))||u.indexOf("multipart/form-data")>-1){const b=this.env&&this.env.FormData;return ou(h?{"files[]":a}:a,b&&new b,this.formSerializer)}}return f||s?(r.setContentType("application/json",!1),a1(a)):a}],transformResponse:[function(a){const r=this.transitional||Da.transitional,u=r&&r.forcedJSONParsing,s=this.responseType==="json";if(L.isResponse(a)||L.isReadableStream(a))return a;if(a&&L.isString(a)&&(u&&!this.responseType||s)){const d=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(a)}catch(h){if(d)throw h.name==="SyntaxError"?ge.from(h,ge.ERR_BAD_RESPONSE,this,null,this.response):h}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ht.classes.FormData,Blob:ht.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};L.forEach(["delete","get","head","post","put","patch"],l=>{Da.headers[l]={}});const r1=L.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),u1=l=>{const a={};let r,u,s;return l&&l.split(`
`).forEach(function(d){s=d.indexOf(":"),r=d.substring(0,s).trim().toLowerCase(),u=d.substring(s+1).trim(),!(!r||a[r]&&r1[r])&&(r==="set-cookie"?a[r]?a[r].push(u):a[r]=[u]:a[r]=a[r]?a[r]+", "+u:u)}),a},Up=Symbol("internals");function xa(l){return l&&String(l).trim().toLowerCase()}function $r(l){return l===!1||l==null?l:L.isArray(l)?l.map($r):String(l)}function o1(l){const a=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let u;for(;u=r.exec(l);)a[u[1]]=u[2];return a}const s1=l=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(l.trim());function Us(l,a,r,u,s){if(L.isFunction(u))return u.call(this,a,r);if(s&&(a=r),!!L.isString(a)){if(L.isString(u))return a.indexOf(u)!==-1;if(L.isRegExp(u))return u.test(a)}}function c1(l){return l.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,r,u)=>r.toUpperCase()+u)}function f1(l,a){const r=L.toCamelCase(" "+a);["get","set","has"].forEach(u=>{Object.defineProperty(l,u+r,{value:function(s,f,d){return this[u].call(this,a,s,f,d)},configurable:!0})})}let wt=class{constructor(a){a&&this.set(a)}set(a,r,u){const s=this;function f(h,b,p){const m=xa(b);if(!m)throw new Error("header name must be a non-empty string");const S=L.findKey(s,m);(!S||s[S]===void 0||p===!0||p===void 0&&s[S]!==!1)&&(s[S||b]=$r(h))}const d=(h,b)=>L.forEach(h,(p,m)=>f(p,m,b));if(L.isPlainObject(a)||a instanceof this.constructor)d(a,r);else if(L.isString(a)&&(a=a.trim())&&!s1(a))d(u1(a),r);else if(L.isObject(a)&&L.isIterable(a)){let h={},b,p;for(const m of a){if(!L.isArray(m))throw TypeError("Object iterator must return a key-value pair");h[p=m[0]]=(b=h[p])?L.isArray(b)?[...b,m[1]]:[b,m[1]]:m[1]}d(h,r)}else a!=null&&f(r,a,u);return this}get(a,r){if(a=xa(a),a){const u=L.findKey(this,a);if(u){const s=this[u];if(!r)return s;if(r===!0)return o1(s);if(L.isFunction(r))return r.call(this,s,u);if(L.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,r){if(a=xa(a),a){const u=L.findKey(this,a);return!!(u&&this[u]!==void 0&&(!r||Us(this,this[u],u,r)))}return!1}delete(a,r){const u=this;let s=!1;function f(d){if(d=xa(d),d){const h=L.findKey(u,d);h&&(!r||Us(u,u[h],h,r))&&(delete u[h],s=!0)}}return L.isArray(a)?a.forEach(f):f(a),s}clear(a){const r=Object.keys(this);let u=r.length,s=!1;for(;u--;){const f=r[u];(!a||Us(this,this[f],f,a,!0))&&(delete this[f],s=!0)}return s}normalize(a){const r=this,u={};return L.forEach(this,(s,f)=>{const d=L.findKey(u,f);if(d){r[d]=$r(s),delete r[f];return}const h=a?c1(f):String(f).trim();h!==f&&delete r[f],r[h]=$r(s),u[h]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const r=Object.create(null);return L.forEach(this,(u,s)=>{u!=null&&u!==!1&&(r[s]=a&&L.isArray(u)?u.join(", "):u)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,r])=>a+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...r){const u=new this(a);return r.forEach(s=>u.set(s)),u}static accessor(a){const u=(this[Up]=this[Up]={accessors:{}}).accessors,s=this.prototype;function f(d){const h=xa(d);u[h]||(f1(s,d),u[h]=!0)}return L.isArray(a)?a.forEach(f):f(a),this}};wt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);L.reduceDescriptors(wt.prototype,({value:l},a)=>{let r=a[0].toUpperCase()+a.slice(1);return{get:()=>l,set(u){this[r]=u}}});L.freezeMethods(wt);function Bs(l,a){const r=this||Da,u=a||r,s=wt.from(u.headers);let f=u.data;return L.forEach(l,function(h){f=h.call(r,f,s.normalize(),a?a.status:void 0)}),s.normalize(),f}function Zm(l){return!!(l&&l.__CANCEL__)}function pi(l,a,r){ge.call(this,l??"canceled",ge.ERR_CANCELED,a,r),this.name="CanceledError"}L.inherits(pi,ge,{__CANCEL__:!0});function Km(l,a,r){const u=r.config.validateStatus;!r.status||!u||u(r.status)?l(r):a(new ge("Request failed with status code "+r.status,[ge.ERR_BAD_REQUEST,ge.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function d1(l){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(l);return a&&a[1]||""}function h1(l,a){l=l||10;const r=new Array(l),u=new Array(l);let s=0,f=0,d;return a=a!==void 0?a:1e3,function(b){const p=Date.now(),m=u[f];d||(d=p),r[s]=b,u[s]=p;let S=f,T=0;for(;S!==s;)T+=r[S++],S=S%l;if(s=(s+1)%l,s===f&&(f=(f+1)%l),p-d<a)return;const v=m&&p-m;return v?Math.round(T*1e3/v):void 0}}function p1(l,a){let r=0,u=1e3/a,s,f;const d=(p,m=Date.now())=>{r=m,s=null,f&&(clearTimeout(f),f=null),l(...p)};return[(...p)=>{const m=Date.now(),S=m-r;S>=u?d(p,m):(s=p,f||(f=setTimeout(()=>{f=null,d(s)},u-S)))},()=>s&&d(s)]}const tu=(l,a,r=3)=>{let u=0;const s=h1(50,250);return p1(f=>{const d=f.loaded,h=f.lengthComputable?f.total:void 0,b=d-u,p=s(b),m=d<=h;u=d;const S={loaded:d,total:h,progress:h?d/h:void 0,bytes:b,rate:p||void 0,estimated:p&&h&&m?(h-d)/p:void 0,event:f,lengthComputable:h!=null,[a?"download":"upload"]:!0};l(S)},r)},Bp=(l,a)=>{const r=l!=null;return[u=>a[0]({lengthComputable:r,total:l,loaded:u}),a[1]]},Lp=l=>(...a)=>L.asap(()=>l(...a)),m1=ht.hasStandardBrowserEnv?((l,a)=>r=>(r=new URL(r,ht.origin),l.protocol===r.protocol&&l.host===r.host&&(a||l.port===r.port)))(new URL(ht.origin),ht.navigator&&/(msie|trident)/i.test(ht.navigator.userAgent)):()=>!0,y1=ht.hasStandardBrowserEnv?{write(l,a,r,u,s,f){const d=[l+"="+encodeURIComponent(a)];L.isNumber(r)&&d.push("expires="+new Date(r).toGMTString()),L.isString(u)&&d.push("path="+u),L.isString(s)&&d.push("domain="+s),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(l){const a=document.cookie.match(new RegExp("(^|;\\s*)("+l+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(l){this.write(l,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function g1(l){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(l)}function b1(l,a){return a?l.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):l}function Jm(l,a,r){let u=!g1(a);return l&&(u||r==!1)?b1(l,a):a}const Hp=l=>l instanceof wt?{...l}:l;function vl(l,a){a=a||{};const r={};function u(p,m,S,T){return L.isPlainObject(p)&&L.isPlainObject(m)?L.merge.call({caseless:T},p,m):L.isPlainObject(m)?L.merge({},m):L.isArray(m)?m.slice():m}function s(p,m,S,T){if(L.isUndefined(m)){if(!L.isUndefined(p))return u(void 0,p,S,T)}else return u(p,m,S,T)}function f(p,m){if(!L.isUndefined(m))return u(void 0,m)}function d(p,m){if(L.isUndefined(m)){if(!L.isUndefined(p))return u(void 0,p)}else return u(void 0,m)}function h(p,m,S){if(S in a)return u(p,m);if(S in l)return u(void 0,p)}const b={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:h,headers:(p,m,S)=>s(Hp(p),Hp(m),S,!0)};return L.forEach(Object.keys({...l,...a}),function(m){const S=b[m]||s,T=S(l[m],a[m],m);L.isUndefined(T)&&S!==h||(r[m]=T)}),r}const Fm=l=>{const a=vl({},l);let{data:r,withXSRFToken:u,xsrfHeaderName:s,xsrfCookieName:f,headers:d,auth:h}=a;a.headers=d=wt.from(d),a.url=Xm(Jm(a.baseURL,a.url,a.allowAbsoluteUrls),l.params,l.paramsSerializer),h&&d.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let b;if(L.isFormData(r)){if(ht.hasStandardBrowserEnv||ht.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((b=d.getContentType())!==!1){const[p,...m]=b?b.split(";").map(S=>S.trim()).filter(Boolean):[];d.setContentType([p||"multipart/form-data",...m].join("; "))}}if(ht.hasStandardBrowserEnv&&(u&&L.isFunction(u)&&(u=u(a)),u||u!==!1&&m1(a.url))){const p=s&&f&&y1.read(f);p&&d.set(s,p)}return a},x1=typeof XMLHttpRequest<"u",S1=x1&&function(l){return new Promise(function(r,u){const s=Fm(l);let f=s.data;const d=wt.from(s.headers).normalize();let{responseType:h,onUploadProgress:b,onDownloadProgress:p}=s,m,S,T,v,_;function U(){v&&v(),_&&_(),s.cancelToken&&s.cancelToken.unsubscribe(m),s.signal&&s.signal.removeEventListener("abort",m)}let C=new XMLHttpRequest;C.open(s.method.toUpperCase(),s.url,!0),C.timeout=s.timeout;function N(){if(!C)return;const H=wt.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),F={data:!h||h==="text"||h==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:H,config:l,request:C};Km(function(oe){r(oe),U()},function(oe){u(oe),U()},F),C=null}"onloadend"in C?C.onloadend=N:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(N)},C.onabort=function(){C&&(u(new ge("Request aborted",ge.ECONNABORTED,l,C)),C=null)},C.onerror=function(){u(new ge("Network Error",ge.ERR_NETWORK,l,C)),C=null},C.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const F=s.transitional||Gm;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),u(new ge(P,F.clarifyTimeoutError?ge.ETIMEDOUT:ge.ECONNABORTED,l,C)),C=null},f===void 0&&d.setContentType(null),"setRequestHeader"in C&&L.forEach(d.toJSON(),function(P,F){C.setRequestHeader(F,P)}),L.isUndefined(s.withCredentials)||(C.withCredentials=!!s.withCredentials),h&&h!=="json"&&(C.responseType=s.responseType),p&&([T,_]=tu(p,!0),C.addEventListener("progress",T)),b&&C.upload&&([S,v]=tu(b),C.upload.addEventListener("progress",S),C.upload.addEventListener("loadend",v)),(s.cancelToken||s.signal)&&(m=H=>{C&&(u(!H||H.type?new pi(null,l,C):H),C.abort(),C=null)},s.cancelToken&&s.cancelToken.subscribe(m),s.signal&&(s.signal.aborted?m():s.signal.addEventListener("abort",m)));const j=d1(s.url);if(j&&ht.protocols.indexOf(j)===-1){u(new ge("Unsupported protocol "+j+":",ge.ERR_BAD_REQUEST,l));return}C.send(f||null)})},v1=(l,a)=>{const{length:r}=l=l?l.filter(Boolean):[];if(a||r){let u=new AbortController,s;const f=function(p){if(!s){s=!0,h();const m=p instanceof Error?p:this.reason;u.abort(m instanceof ge?m:new pi(m instanceof Error?m.message:m))}};let d=a&&setTimeout(()=>{d=null,f(new ge(`timeout ${a} of ms exceeded`,ge.ETIMEDOUT))},a);const h=()=>{l&&(d&&clearTimeout(d),d=null,l.forEach(p=>{p.unsubscribe?p.unsubscribe(f):p.removeEventListener("abort",f)}),l=null)};l.forEach(p=>p.addEventListener("abort",f));const{signal:b}=u;return b.unsubscribe=()=>L.asap(h),b}},E1=function*(l,a){let r=l.byteLength;if(r<a){yield l;return}let u=0,s;for(;u<r;)s=u+a,yield l.slice(u,s),u=s},T1=async function*(l,a){for await(const r of A1(l))yield*E1(r,a)},A1=async function*(l){if(l[Symbol.asyncIterator]){yield*l;return}const a=l.getReader();try{for(;;){const{done:r,value:u}=await a.read();if(r)break;yield u}}finally{await a.cancel()}},qp=(l,a,r,u)=>{const s=T1(l,a);let f=0,d,h=b=>{d||(d=!0,u&&u(b))};return new ReadableStream({async pull(b){try{const{done:p,value:m}=await s.next();if(p){h(),b.close();return}let S=m.byteLength;if(r){let T=f+=S;r(T)}b.enqueue(new Uint8Array(m))}catch(p){throw h(p),p}},cancel(b){return h(b),s.return()}},{highWaterMark:2})},su=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Pm=su&&typeof ReadableStream=="function",w1=su&&(typeof TextEncoder=="function"?(l=>a=>l.encode(a))(new TextEncoder):async l=>new Uint8Array(await new Response(l).arrayBuffer())),Im=(l,...a)=>{try{return!!l(...a)}catch{return!1}},O1=Pm&&Im(()=>{let l=!1;const a=new Request(ht.origin,{body:new ReadableStream,method:"POST",get duplex(){return l=!0,"half"}}).headers.has("Content-Type");return l&&!a}),Yp=64*1024,tc=Pm&&Im(()=>L.isReadableStream(new Response("").body)),nu={stream:tc&&(l=>l.body)};su&&(l=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!nu[a]&&(nu[a]=L.isFunction(l[a])?r=>r[a]():(r,u)=>{throw new ge(`Response type '${a}' is not supported`,ge.ERR_NOT_SUPPORT,u)})})})(new Response);const _1=async l=>{if(l==null)return 0;if(L.isBlob(l))return l.size;if(L.isSpecCompliantForm(l))return(await new Request(ht.origin,{method:"POST",body:l}).arrayBuffer()).byteLength;if(L.isArrayBufferView(l)||L.isArrayBuffer(l))return l.byteLength;if(L.isURLSearchParams(l)&&(l=l+""),L.isString(l))return(await w1(l)).byteLength},C1=async(l,a)=>{const r=L.toFiniteNumber(l.getContentLength());return r??_1(a)},N1=su&&(async l=>{let{url:a,method:r,data:u,signal:s,cancelToken:f,timeout:d,onDownloadProgress:h,onUploadProgress:b,responseType:p,headers:m,withCredentials:S="same-origin",fetchOptions:T}=Fm(l);p=p?(p+"").toLowerCase():"text";let v=v1([s,f&&f.toAbortSignal()],d),_;const U=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let C;try{if(b&&O1&&r!=="get"&&r!=="head"&&(C=await C1(m,u))!==0){let F=new Request(a,{method:"POST",body:u,duplex:"half"}),q;if(L.isFormData(u)&&(q=F.headers.get("content-type"))&&m.setContentType(q),F.body){const[oe,ye]=Bp(C,tu(Lp(b)));u=qp(F.body,Yp,oe,ye)}}L.isString(S)||(S=S?"include":"omit");const N="credentials"in Request.prototype;_=new Request(a,{...T,signal:v,method:r.toUpperCase(),headers:m.normalize().toJSON(),body:u,duplex:"half",credentials:N?S:void 0});let j=await fetch(_,T);const H=tc&&(p==="stream"||p==="response");if(tc&&(h||H&&U)){const F={};["status","statusText","headers"].forEach(me=>{F[me]=j[me]});const q=L.toFiniteNumber(j.headers.get("content-length")),[oe,ye]=h&&Bp(q,tu(Lp(h),!0))||[];j=new Response(qp(j.body,Yp,oe,()=>{ye&&ye(),U&&U()}),F)}p=p||"text";let P=await nu[L.findKey(nu,p)||"text"](j,l);return!H&&U&&U(),await new Promise((F,q)=>{Km(F,q,{data:P,headers:wt.from(j.headers),status:j.status,statusText:j.statusText,config:l,request:_})})}catch(N){throw U&&U(),N&&N.name==="TypeError"&&/Load failed|fetch/i.test(N.message)?Object.assign(new ge("Network Error",ge.ERR_NETWORK,l,_),{cause:N.cause||N}):ge.from(N,N&&N.code,l,_)}}),nc={http:Gb,xhr:S1,fetch:N1};L.forEach(nc,(l,a)=>{if(l){try{Object.defineProperty(l,"name",{value:a})}catch{}Object.defineProperty(l,"adapterName",{value:a})}});const Vp=l=>`- ${l}`,R1=l=>L.isFunction(l)||l===null||l===!1,$m={getAdapter:l=>{l=L.isArray(l)?l:[l];const{length:a}=l;let r,u;const s={};for(let f=0;f<a;f++){r=l[f];let d;if(u=r,!R1(r)&&(u=nc[(d=String(r)).toLowerCase()],u===void 0))throw new ge(`Unknown adapter '${d}'`);if(u)break;s[d||"#"+f]=u}if(!u){const f=Object.entries(s).map(([h,b])=>`adapter ${h} `+(b===!1?"is not supported by the environment":"is not available in the build"));let d=a?f.length>1?`since :
`+f.map(Vp).join(`
`):" "+Vp(f[0]):"as no adapter specified";throw new ge("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return u},adapters:nc};function Ls(l){if(l.cancelToken&&l.cancelToken.throwIfRequested(),l.signal&&l.signal.aborted)throw new pi(null,l)}function Xp(l){return Ls(l),l.headers=wt.from(l.headers),l.data=Bs.call(l,l.transformRequest),["post","put","patch"].indexOf(l.method)!==-1&&l.headers.setContentType("application/x-www-form-urlencoded",!1),$m.getAdapter(l.adapter||Da.adapter)(l).then(function(u){return Ls(l),u.data=Bs.call(l,l.transformResponse,u),u.headers=wt.from(u.headers),u},function(u){return Zm(u)||(Ls(l),u&&u.response&&(u.response.data=Bs.call(l,l.transformResponse,u.response),u.response.headers=wt.from(u.response.headers))),Promise.reject(u)})}const Wm="1.11.0",cu={};["object","boolean","number","function","string","symbol"].forEach((l,a)=>{cu[l]=function(u){return typeof u===l||"a"+(a<1?"n ":" ")+l}});const Gp={};cu.transitional=function(a,r,u){function s(f,d){return"[Axios v"+Wm+"] Transitional option '"+f+"'"+d+(u?". "+u:"")}return(f,d,h)=>{if(a===!1)throw new ge(s(d," has been removed"+(r?" in "+r:"")),ge.ERR_DEPRECATED);return r&&!Gp[d]&&(Gp[d]=!0,console.warn(s(d," has been deprecated since v"+r+" and will be removed in the near future"))),a?a(f,d,h):!0}};cu.spelling=function(a){return(r,u)=>(console.warn(`${u} is likely a misspelling of ${a}`),!0)};function z1(l,a,r){if(typeof l!="object")throw new ge("options must be an object",ge.ERR_BAD_OPTION_VALUE);const u=Object.keys(l);let s=u.length;for(;s-- >0;){const f=u[s],d=a[f];if(d){const h=l[f],b=h===void 0||d(h,f,l);if(b!==!0)throw new ge("option "+f+" must be "+b,ge.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new ge("Unknown option "+f,ge.ERR_BAD_OPTION)}}const Wr={assertOptions:z1,validators:cu},cn=Wr.validators;let Sl=class{constructor(a){this.defaults=a||{},this.interceptors={request:new jp,response:new jp}}async request(a,r){try{return await this._request(a,r)}catch(u){if(u instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const f=s.stack?s.stack.replace(/^.+\n/,""):"";try{u.stack?f&&!String(u.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(u.stack+=`
`+f):u.stack=f}catch{}}throw u}}_request(a,r){typeof a=="string"?(r=r||{},r.url=a):r=a||{},r=vl(this.defaults,r);const{transitional:u,paramsSerializer:s,headers:f}=r;u!==void 0&&Wr.assertOptions(u,{silentJSONParsing:cn.transitional(cn.boolean),forcedJSONParsing:cn.transitional(cn.boolean),clarifyTimeoutError:cn.transitional(cn.boolean)},!1),s!=null&&(L.isFunction(s)?r.paramsSerializer={serialize:s}:Wr.assertOptions(s,{encode:cn.function,serialize:cn.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Wr.assertOptions(r,{baseUrl:cn.spelling("baseURL"),withXsrfToken:cn.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let d=f&&L.merge(f.common,f[r.method]);f&&L.forEach(["delete","get","head","post","put","patch","common"],_=>{delete f[_]}),r.headers=wt.concat(d,f);const h=[];let b=!0;this.interceptors.request.forEach(function(U){typeof U.runWhen=="function"&&U.runWhen(r)===!1||(b=b&&U.synchronous,h.unshift(U.fulfilled,U.rejected))});const p=[];this.interceptors.response.forEach(function(U){p.push(U.fulfilled,U.rejected)});let m,S=0,T;if(!b){const _=[Xp.bind(this),void 0];for(_.unshift(...h),_.push(...p),T=_.length,m=Promise.resolve(r);S<T;)m=m.then(_[S++],_[S++]);return m}T=h.length;let v=r;for(S=0;S<T;){const _=h[S++],U=h[S++];try{v=_(v)}catch(C){U.call(this,C);break}}try{m=Xp.call(this,v)}catch(_){return Promise.reject(_)}for(S=0,T=p.length;S<T;)m=m.then(p[S++],p[S++]);return m}getUri(a){a=vl(this.defaults,a);const r=Jm(a.baseURL,a.url,a.allowAbsoluteUrls);return Xm(r,a.params,a.paramsSerializer)}};L.forEach(["delete","get","head","options"],function(a){Sl.prototype[a]=function(r,u){return this.request(vl(u||{},{method:a,url:r,data:(u||{}).data}))}});L.forEach(["post","put","patch"],function(a){function r(u){return function(f,d,h){return this.request(vl(h||{},{method:a,headers:u?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}Sl.prototype[a]=r(),Sl.prototype[a+"Form"]=r(!0)});let D1=class ey{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(f){r=f});const u=this;this.promise.then(s=>{if(!u._listeners)return;let f=u._listeners.length;for(;f-- >0;)u._listeners[f](s);u._listeners=null}),this.promise.then=s=>{let f;const d=new Promise(h=>{u.subscribe(h),f=h}).then(s);return d.cancel=function(){u.unsubscribe(f)},d},a(function(f,d,h){u.reason||(u.reason=new pi(f,d,h),r(u.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const r=this._listeners.indexOf(a);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const a=new AbortController,r=u=>{a.abort(u)};return this.subscribe(r),a.signal.unsubscribe=()=>this.unsubscribe(r),a.signal}static source(){let a;return{token:new ey(function(s){a=s}),cancel:a}}};function k1(l){return function(r){return l.apply(null,r)}}function M1(l){return L.isObject(l)&&l.isAxiosError===!0}const lc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(lc).forEach(([l,a])=>{lc[a]=l});function ty(l){const a=new Sl(l),r=zm(Sl.prototype.request,a);return L.extend(r,Sl.prototype,a,{allOwnKeys:!0}),L.extend(r,a,null,{allOwnKeys:!0}),r.create=function(s){return ty(vl(l,s))},r}const He=ty(Da);He.Axios=Sl;He.CanceledError=pi;He.CancelToken=D1;He.isCancel=Zm;He.VERSION=Wm;He.toFormData=ou;He.AxiosError=ge;He.Cancel=He.CanceledError;He.all=function(a){return Promise.all(a)};He.spread=k1;He.isAxiosError=M1;He.mergeConfig=vl;He.AxiosHeaders=wt;He.formToJSON=l=>Qm(L.isHTMLForm(l)?new FormData(l):l);He.getAdapter=$m.getAdapter;He.HttpStatusCode=lc;He.default=He;const{Axios:f2,AxiosError:d2,CanceledError:h2,isCancel:p2,CancelToken:m2,VERSION:y2,all:g2,Cancel:b2,isAxiosError:x2,spread:S2,toFormData:v2,AxiosHeaders:E2,HttpStatusCode:T2,formToJSON:A2,getAdapter:w2,mergeConfig:O2}=He;function Qp({onSubjectSelect:l,onCreateSubject:a}){const[r,u]=Ve.useState([]),[s,f]=Ve.useState(!0),[d,h]=Ve.useState(null);Ve.useEffect(()=>{b()},[]);const b=async()=>{try{f(!0);const m=await He.get("http://localhost:3001/api/subjects");u(m.data),h(null)}catch(m){console.error("Error fetching subjects:",m),h("Failed to load subjects. Please try again.")}finally{f(!1)}},p=async(m,S)=>{if(window.confirm(`Are you sure you want to delete "${S}"? This will also delete all associated curricula and courses.`))try{await He.delete(`http://localhost:3001/api/subjects/${m}`),u(r.filter(T=>T.id!==m))}catch(T){console.error("Error deleting subject:",T),alert("Failed to delete subject. Please try again.")}};return s?z.jsxs("div",{className:"flex flex-col items-center justify-center h-96",children:[z.jsx("div",{className:"w-12 h-12 border-4 border-gray-300 border-t-blue-400 rounded-full animate-spin"}),z.jsx("p",{className:"mt-5 text-gray-400",children:"Loading your subjects..."})]}):d?z.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center text-gray-400",children:[z.jsx("h2",{className:"text-red-400 mb-2",children:"Error"}),z.jsx("p",{children:d}),z.jsx("button",{onClick:b,className:"bg-blue-600 text-white border-none px-5 py-2 rounded cursor-pointer mt-4 hover:bg-blue-700 transition-colors duration-200",children:"Try Again"})]}):z.jsxs("div",{className:"p-5 max-w-6xl mx-auto",children:[z.jsxs("div",{className:"flex justify-between items-center mb-8",children:[z.jsx("h1",{className:"text-blue-400 m-0 text-3xl",children:"Your Learning Subjects"}),z.jsx("button",{className:"bg-blue-600 text-white border-none px-5 py-3 rounded-md cursor-pointer font-bold transition-colors duration-200 hover:bg-blue-700",onClick:a,children:"+ Create New Subject"})]}),r.length===0?z.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center text-gray-400",children:[z.jsx("h2",{className:"text-blue-400 mb-2",children:"No subjects yet"}),z.jsx("p",{children:"Create your first learning subject to get started!"}),z.jsx("button",{className:"bg-blue-600 text-white border-none px-8 py-4 rounded-md cursor-pointer font-bold text-base mt-5 transition-colors duration-200 hover:bg-blue-700",onClick:a,children:"Create Your First Subject"})]}):z.jsx("div",{className:"grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-5",children:r.map(m=>z.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 border border-gray-600 transition-all duration-200 hover:border-blue-400 hover:-translate-y-0.5",children:[z.jsxs("div",{className:"flex justify-between items-start mb-2",children:[z.jsx("h3",{className:"text-blue-400 m-0 text-lg flex-1",children:m.title}),z.jsx("div",{className:"flex gap-1",children:z.jsx("button",{className:"bg-transparent border-none text-red-400 cursor-pointer text-xl w-6 h-6 flex items-center justify-center rounded transition-colors duration-200 hover:bg-red-400/10",onClick:()=>p(m.id,m.title),title:"Delete subject",children:"×"})})]}),m.description&&z.jsx("p",{className:"text-gray-400 my-2 text-sm leading-relaxed",children:m.description}),z.jsxs("div",{className:"flex flex-col gap-1 my-4 text-xs",children:[z.jsx("span",{className:"text-blue-400",children:m.has_curriculum?"✓ Curriculum ready":"⚠ No curriculum"}),z.jsxs("span",{className:"text-gray-500",children:["Created ",new Date(m.created_at).toLocaleDateString()]})]}),z.jsx("button",{className:`w-full bg-blue-600 text-white border-none py-2 rounded cursor-pointer font-bold transition-colors duration-200 ${m.has_curriculum?"hover:bg-blue-700":"bg-gray-600 cursor-not-allowed opacity-60"}`,onClick:()=>l(m),disabled:!m.has_curriculum,children:m.has_curriculum?"Start Learning":"Setup Required"})]},m.id))})]})}function j1({curriculum:l,onCurriculumChange:a}){const[r,u]=Ve.useState(null),[s,f]=Ve.useState(null),d=()=>{const C={id:`category-${Date.now()}`,title:"New Category",description:"",topics:[]},N={...l,curriculum:[...l.curriculum,C]};a(N),u(C.id)},h=C=>{const N={...l,curriculum:l.curriculum.filter(j=>j.id!==C)};a(N)},b=(C,N,j)=>{const H={...l,curriculum:l.curriculum.map(P=>P.id===C?{...P,[N]:j}:P)};a(H)},p=C=>{const N={id:`topic-${Date.now()}`,title:"New Topic",description:""},j={...l,curriculum:l.curriculum.map(H=>H.id===C?{...H,topics:[...H.topics,N]}:H)};a(j),f(N.id)},m=(C,N)=>{const j={...l,curriculum:l.curriculum.map(H=>H.id===C?{...H,topics:H.topics.filter(P=>P.id!==N)}:H)};a(j)},S=(C,N,j,H)=>{const P={...l,curriculum:l.curriculum.map(F=>F.id===C?{...F,topics:F.topics.map(q=>q.id===N?{...q,[j]:H}:q)}:F)};a(P)},T=C=>{if(C===0)return;const N=[...l.curriculum];[N[C-1],N[C]]=[N[C],N[C-1]],a({...l,curriculum:N})},v=C=>{if(C===l.curriculum.length-1)return;const N=[...l.curriculum];[N[C],N[C+1]]=[N[C+1],N[C]],a({...l,curriculum:N})},_=(C,N)=>{if(N===0)return;const j={...l,curriculum:l.curriculum.map(H=>{if(H.id===C){const P=[...H.topics];return[P[N-1],P[N]]=[P[N],P[N-1]],{...H,topics:P}}return H})};a(j)},U=(C,N,j)=>{if(N===j-1)return;const H={...l,curriculum:l.curriculum.map(P=>{if(P.id===C){const F=[...P.topics];return[F[N],F[N+1]]=[F[N+1],F[N]],{...P,topics:F}}return P})};a(H)};return z.jsxs("div",{className:"bg-gray-800 rounded-lg p-5 border border-gray-600",children:[z.jsxs("div",{className:"flex justify-between items-center mb-5 pb-4 border-b border-gray-600",children:[z.jsx("h3",{className:"text-blue-400 m-0 text-lg",children:"Edit Curriculum"}),z.jsx("button",{onClick:d,className:"bg-blue-600 text-white border-none px-4 py-2 rounded cursor-pointer font-bold transition-colors duration-200 hover:bg-blue-700",children:"+ Add Category"})]}),z.jsx("div",{className:"flex flex-col gap-5",children:l.curriculum.map((C,N)=>z.jsxs("div",{className:"bg-gray-700 rounded-md p-4 border border-gray-600",children:[z.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[z.jsxs("div",{className:"flex gap-1",children:[z.jsx("button",{onClick:()=>T(N),disabled:N===0,className:`bg-transparent border border-gray-500 text-gray-400 w-6 h-6 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 ${N===0?"opacity-50 cursor-not-allowed":"hover:bg-blue-400 hover:border-blue-400"}`,title:"Move up",children:"↑"}),z.jsx("button",{onClick:()=>v(N),disabled:N===l.curriculum.length-1,className:`bg-transparent border border-gray-500 text-gray-400 w-6 h-6 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 ${N===l.curriculum.length-1?"opacity-50 cursor-not-allowed":"hover:bg-blue-400 hover:border-blue-400"}`,title:"Move down",children:"↓"}),z.jsx("button",{onClick:()=>h(C.id),className:"bg-transparent border border-red-400 text-red-400 w-6 h-6 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 hover:bg-red-400 hover:text-white",title:"Delete category",children:"×"})]}),r===C.id?z.jsx("input",{type:"text",value:C.title,onChange:j=>b(C.id,"title",j.target.value),onBlur:()=>u(null),onKeyPress:j=>j.key==="Enter"&&u(null),className:"flex-1 p-2 bg-gray-600 border border-gray-500 rounded text-gray-300 text-sm focus:outline-none focus:border-blue-400",autoFocus:!0}):z.jsx("h4",{className:"text-blue-400 m-0 text-base cursor-pointer flex-1 hover:text-blue-300",onClick:()=>u(C.id),children:C.title})]}),z.jsx("textarea",{value:C.description,onChange:j=>b(C.id,"description",j.target.value),placeholder:"Category description...",className:"w-full p-2 bg-gray-600 border border-gray-500 rounded text-gray-300 text-sm mb-4 resize-y min-h-12 focus:outline-none focus:border-blue-400",rows:2}),z.jsxs("div",{className:"space-y-3",children:[C.topics.map((j,H)=>z.jsxs("div",{className:"bg-gray-600 rounded p-3 border border-gray-500",children:[z.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[z.jsxs("div",{className:"flex gap-1",children:[z.jsx("button",{onClick:()=>_(C.id,H),disabled:H===0,className:`bg-transparent border border-gray-400 text-gray-300 w-5 h-5 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 ${H===0?"opacity-50 cursor-not-allowed":"hover:bg-blue-400 hover:border-blue-400"}`,title:"Move up",children:"↑"}),z.jsx("button",{onClick:()=>U(C.id,H,C.topics.length),disabled:H===C.topics.length-1,className:`bg-transparent border border-gray-400 text-gray-300 w-5 h-5 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 ${H===C.topics.length-1?"opacity-50 cursor-not-allowed":"hover:bg-blue-400 hover:border-blue-400"}`,title:"Move down",children:"↓"}),z.jsx("button",{onClick:()=>m(C.id,j.id),className:"bg-transparent border border-red-400 text-red-400 w-5 h-5 rounded cursor-pointer flex items-center justify-center text-xs transition-all duration-200 hover:bg-red-400 hover:text-white",title:"Delete topic",children:"×"})]}),s===j.id?z.jsx("input",{type:"text",value:j.title,onChange:P=>S(C.id,j.id,"title",P.target.value),onBlur:()=>f(null),onKeyPress:P=>P.key==="Enter"&&f(null),className:"flex-1 p-2 bg-gray-500 border border-gray-400 rounded text-gray-300 text-sm focus:outline-none focus:border-blue-400",autoFocus:!0}):z.jsx("span",{className:"text-gray-300 font-medium cursor-pointer flex-1 hover:text-blue-400",onClick:()=>f(j.id),children:j.title})]}),z.jsx("input",{type:"text",value:j.description,onChange:P=>S(C.id,j.id,"description",P.target.value),placeholder:"Topic description...",className:"w-full p-2 bg-gray-500 border border-gray-400 rounded text-gray-300 text-sm mt-2 focus:outline-none focus:border-blue-400"})]},j.id)),z.jsx("button",{onClick:()=>p(C.id),className:"bg-blue-600 text-white border-none px-4 py-2 rounded cursor-pointer font-bold transition-colors duration-200 hover:bg-blue-700 mt-3",children:"+ Add Topic"})]})]},C.id))})]})}function U1({onBack:l,onSubjectCreated:a,apiKey:r}){const[u,s]=Ve.useState(1),[f,d]=Ve.useState(""),[h,b]=Ve.useState(""),[p,m]=Ve.useState(null),[S,T]=Ve.useState(!1),[v,_]=Ve.useState(null),U=async()=>{if(!f.trim()){_("Please enter a subject title");return}if(!r){_("Please set your API key in the settings");return}T(!0),_(null);try{const j=await He.post("http://localhost:3001/api/generate-curriculum",{subject:f.trim()});m(j.data),s(2)}catch(j){console.error("Error generating curriculum:",j),j.response?_(`Error: ${j.response.data.error||"Unknown error"}`):j.request?_("Error: No response from server. Make sure the backend is running."):_(`Error: ${j.message}`)}finally{T(!1)}},C=async()=>{try{const H=(await He.post("http://localhost:3001/api/subjects",{title:f.trim(),description:h.trim()})).data.id;await He.post("http://localhost:3001/api/curricula",{subjectId:H,structure:p}),a({id:H,title:f.trim(),description:h.trim(),has_curriculum:!0})}catch(j){console.error("Error creating subject:",j),_("Failed to create subject. Please try again.")}},N=j=>{m(j)};return u===1?z.jsxs("div",{className:"p-5 max-w-4xl mx-auto",children:[z.jsxs("div",{className:"flex items-center gap-5 mb-8",children:[z.jsx("button",{onClick:l,className:"bg-transparent border border-blue-400 text-blue-400 px-4 py-2 rounded cursor-pointer transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"← Back to Dashboard"}),z.jsx("h1",{className:"text-blue-400 m-0 text-2xl",children:"Create New Learning Subject"})]}),z.jsxs("div",{className:"bg-gray-800 p-8 rounded-lg border border-gray-600",children:[z.jsxs("div",{className:"mb-5",children:[z.jsx("label",{htmlFor:"subjectTitle",className:"block mb-2 font-bold text-gray-300",children:"What do you want to learn?"}),z.jsx("input",{id:"subjectTitle",type:"text",value:f,onChange:j=>d(j.target.value),className:"w-full p-3 bg-gray-700 border border-gray-600 rounded text-gray-300 text-sm font-inherit focus:outline-none focus:border-blue-400",placeholder:"e.g., React.js, Machine Learning, Photography...",maxLength:100})]}),z.jsxs("div",{className:"mb-5",children:[z.jsx("label",{htmlFor:"subjectDescription",className:"block mb-2 font-bold text-gray-300",children:"Description (optional)"}),z.jsx("textarea",{id:"subjectDescription",value:h,onChange:j=>b(j.target.value),className:"w-full p-3 bg-gray-700 border border-gray-600 rounded text-gray-300 text-sm font-inherit resize-y min-h-20 focus:outline-none focus:border-blue-400",placeholder:"Add any specific details about what you want to focus on...",maxLength:500,rows:3})]}),v&&z.jsx("div",{className:"bg-red-400/10 border border-red-400 text-red-400 p-3 rounded mb-5",children:v}),z.jsx("button",{onClick:U,disabled:S||!f.trim(),className:`bg-blue-600 text-white border-none px-6 py-3 rounded cursor-pointer font-bold text-base transition-colors duration-200 w-full ${S||!f.trim()?"bg-gray-600 cursor-not-allowed opacity-60":"hover:bg-blue-700"}`,children:S?"Generating Curriculum...":"Generate Curriculum"})]})]}):u===2?z.jsxs("div",{className:"p-5 max-w-4xl mx-auto",children:[z.jsxs("div",{className:"flex items-center gap-5 mb-8",children:[z.jsx("button",{onClick:()=>s(1),className:"bg-transparent border border-blue-400 text-blue-400 px-4 py-2 rounded cursor-pointer transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"← Back to Subject Details"}),z.jsx("h1",{className:"text-blue-400 m-0 text-2xl",children:"Review Your Curriculum"})]}),z.jsxs("div",{className:"bg-gray-800 p-8 rounded-lg border border-gray-600",children:[z.jsxs("div",{className:"mb-8 pb-5 border-b border-gray-600",children:[z.jsx("h2",{className:"text-blue-400 m-0 mb-2 text-xl",children:f}),h&&z.jsx("p",{className:"text-gray-400 m-0 leading-relaxed",children:h})]}),p&&z.jsx("div",{className:"mb-8",children:p.curriculum.map((j,H)=>z.jsxs("div",{className:"mb-6 p-5 bg-gray-700 rounded-md border border-gray-600",children:[z.jsx("h3",{className:"text-blue-400 m-0 mb-2 text-lg",children:j.title}),j.description&&z.jsx("p",{className:"text-gray-400 m-0 mb-4 text-sm leading-relaxed",children:j.description}),z.jsx("ul",{className:"list-none p-0 m-0",children:j.topics.map((P,F)=>z.jsxs("li",{className:"flex flex-col py-2 border-b border-gray-600 last:border-b-0",children:[z.jsx("span",{className:"text-gray-300 font-medium mb-1",children:P.title}),P.description&&z.jsx("span",{className:"text-gray-400 text-xs leading-tight",children:P.description})]},P.id))})]},j.id))}),z.jsxs("div",{className:"flex gap-4 justify-end",children:[z.jsx("button",{onClick:()=>s(1),className:"bg-transparent border border-blue-400 text-blue-400 px-5 py-2 rounded cursor-pointer font-bold transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"Regenerate Curriculum"}),z.jsx("button",{onClick:()=>s(3),className:"bg-transparent border border-blue-400 text-blue-400 px-5 py-2 rounded cursor-pointer font-bold transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"Edit Curriculum"}),z.jsx("button",{onClick:C,className:"bg-blue-600 text-white border-none px-5 py-2 rounded cursor-pointer font-bold transition-colors duration-200 hover:bg-blue-700",children:"Create Subject"})]})]})]}):u===3?z.jsxs("div",{className:"p-5 max-w-4xl mx-auto",children:[z.jsxs("div",{className:"flex items-center gap-5 mb-8",children:[z.jsx("button",{onClick:()=>s(2),className:"bg-transparent border border-blue-400 text-blue-400 px-4 py-2 rounded cursor-pointer transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"← Back to Review"}),z.jsx("h1",{className:"text-blue-400 m-0 text-2xl",children:"Edit Curriculum"})]}),z.jsxs("div",{className:"bg-gray-800 p-8 rounded-lg border border-gray-600",children:[z.jsxs("div",{className:"mb-8 pb-5 border-b border-gray-600",children:[z.jsx("h2",{className:"text-blue-400 m-0 mb-2 text-xl",children:f}),h&&z.jsx("p",{className:"text-gray-400 m-0 leading-relaxed",children:h})]}),z.jsx(j1,{curriculum:p,onCurriculumChange:N}),z.jsxs("div",{className:"flex gap-4 justify-end mt-8 pt-5 border-t border-gray-600",children:[z.jsx("button",{onClick:()=>s(2),className:"bg-transparent border border-blue-400 text-blue-400 px-5 py-2 rounded cursor-pointer font-bold transition-all duration-200 hover:bg-blue-400 hover:text-white",children:"Back to Review"}),z.jsx("button",{onClick:C,className:"bg-blue-600 text-white border-none px-5 py-2 rounded cursor-pointer font-bold transition-colors duration-200 hover:bg-blue-700",children:"Create Subject"})]})]})]}):null}function B1({curriculum:l,onTopicSelect:a,selectedTopic:r,subjectTitle:u}){const s=f=>{a(f)};return l?z.jsxs("div",{className:"p-4",children:[z.jsx("h2",{className:"text-lg mb-4 text-blue-400",children:u||"Curriculum"}),z.jsx("ul",{className:"list-none p-0 m-0",children:l.map(f=>z.jsxs("li",{className:"mb-5",children:[z.jsx("h3",{className:"text-base mb-2 text-gray-400",children:f.title}),z.jsx("ul",{className:"list-none pl-4 m-0",children:f.topics.map(d=>z.jsx("li",{className:`px-3 py-2 mb-1 rounded cursor-pointer transition-colors duration-200 hover:bg-gray-600 ${r?.id===d.id?"bg-blue-600 text-white":""}`,onClick:()=>s(d),children:d.title},d.id))})]},f.id))})]}):z.jsx("div",{className:"p-4",children:z.jsx("h2",{className:"text-lg mb-4 text-blue-400",children:"Loading..."})})}function L1(l,a){const r={};return(l[l.length-1]===""?[...l,""]:l).join((r.padRight?" ":"")+","+(r.padLeft===!1?"":" ")).trim()}const H1=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,q1=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Y1={};function Zp(l,a){return(Y1.jsx?q1:H1).test(l)}const V1=/[ \t\n\f\r]/g;function X1(l){return typeof l=="object"?l.type==="text"?Kp(l.value):!1:Kp(l)}function Kp(l){return l.replace(V1,"")===""}class ka{constructor(a,r,u){this.normal=r,this.property=a,u&&(this.space=u)}}ka.prototype.normal={};ka.prototype.property={};ka.prototype.space=void 0;function ny(l,a){const r={},u={};for(const s of l)Object.assign(r,s.property),Object.assign(u,s.normal);return new ka(r,u,a)}function ic(l){return l.toLowerCase()}class _t{constructor(a,r){this.attribute=r,this.property=a}}_t.prototype.attribute="";_t.prototype.booleanish=!1;_t.prototype.boolean=!1;_t.prototype.commaOrSpaceSeparated=!1;_t.prototype.commaSeparated=!1;_t.prototype.defined=!1;_t.prototype.mustUseProperty=!1;_t.prototype.number=!1;_t.prototype.overloadedBoolean=!1;_t.prototype.property="";_t.prototype.spaceSeparated=!1;_t.prototype.space=void 0;let G1=0;const xe=El(),$e=El(),ac=El(),K=El(),Ye=El(),fi=El(),Lt=El();function El(){return 2**++G1}const rc=Object.freeze(Object.defineProperty({__proto__:null,boolean:xe,booleanish:$e,commaOrSpaceSeparated:Lt,commaSeparated:fi,number:K,overloadedBoolean:ac,spaceSeparated:Ye},Symbol.toStringTag,{value:"Module"})),Hs=Object.keys(rc);class bc extends _t{constructor(a,r,u,s){let f=-1;if(super(a,r),Jp(this,"space",s),typeof u=="number")for(;++f<Hs.length;){const d=Hs[f];Jp(this,Hs[f],(u&rc[d])===rc[d])}}}bc.prototype.defined=!0;function Jp(l,a,r){r&&(l[a]=r)}function mi(l){const a={},r={};for(const[u,s]of Object.entries(l.properties)){const f=new bc(u,l.transform(l.attributes||{},u),s,l.space);l.mustUseProperty&&l.mustUseProperty.includes(u)&&(f.mustUseProperty=!0),a[u]=f,r[ic(u)]=u,r[ic(f.attribute)]=u}return new ka(a,r,l.space)}const ly=mi({properties:{ariaActiveDescendant:null,ariaAtomic:$e,ariaAutoComplete:null,ariaBusy:$e,ariaChecked:$e,ariaColCount:K,ariaColIndex:K,ariaColSpan:K,ariaControls:Ye,ariaCurrent:null,ariaDescribedBy:Ye,ariaDetails:null,ariaDisabled:$e,ariaDropEffect:Ye,ariaErrorMessage:null,ariaExpanded:$e,ariaFlowTo:Ye,ariaGrabbed:$e,ariaHasPopup:null,ariaHidden:$e,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:Ye,ariaLevel:K,ariaLive:null,ariaModal:$e,ariaMultiLine:$e,ariaMultiSelectable:$e,ariaOrientation:null,ariaOwns:Ye,ariaPlaceholder:null,ariaPosInSet:K,ariaPressed:$e,ariaReadOnly:$e,ariaRelevant:null,ariaRequired:$e,ariaRoleDescription:Ye,ariaRowCount:K,ariaRowIndex:K,ariaRowSpan:K,ariaSelected:$e,ariaSetSize:K,ariaSort:null,ariaValueMax:K,ariaValueMin:K,ariaValueNow:K,ariaValueText:null,role:null},transform(l,a){return a==="role"?a:"aria-"+a.slice(4).toLowerCase()}});function iy(l,a){return a in l?l[a]:a}function ay(l,a){return iy(l,a.toLowerCase())}const Q1=mi({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:fi,acceptCharset:Ye,accessKey:Ye,action:null,allow:null,allowFullScreen:xe,allowPaymentRequest:xe,allowUserMedia:xe,alt:null,as:null,async:xe,autoCapitalize:null,autoComplete:Ye,autoFocus:xe,autoPlay:xe,blocking:Ye,capture:null,charSet:null,checked:xe,cite:null,className:Ye,cols:K,colSpan:null,content:null,contentEditable:$e,controls:xe,controlsList:Ye,coords:K|fi,crossOrigin:null,data:null,dateTime:null,decoding:null,default:xe,defer:xe,dir:null,dirName:null,disabled:xe,download:ac,draggable:$e,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:xe,formTarget:null,headers:Ye,height:K,hidden:ac,high:K,href:null,hrefLang:null,htmlFor:Ye,httpEquiv:Ye,id:null,imageSizes:null,imageSrcSet:null,inert:xe,inputMode:null,integrity:null,is:null,isMap:xe,itemId:null,itemProp:Ye,itemRef:Ye,itemScope:xe,itemType:Ye,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:xe,low:K,manifest:null,max:null,maxLength:K,media:null,method:null,min:null,minLength:K,multiple:xe,muted:xe,name:null,nonce:null,noModule:xe,noValidate:xe,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:xe,optimum:K,pattern:null,ping:Ye,placeholder:null,playsInline:xe,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:xe,referrerPolicy:null,rel:Ye,required:xe,reversed:xe,rows:K,rowSpan:K,sandbox:Ye,scope:null,scoped:xe,seamless:xe,selected:xe,shadowRootClonable:xe,shadowRootDelegatesFocus:xe,shadowRootMode:null,shape:null,size:K,sizes:null,slot:null,span:K,spellCheck:$e,src:null,srcDoc:null,srcLang:null,srcSet:null,start:K,step:null,style:null,tabIndex:K,target:null,title:null,translate:null,type:null,typeMustMatch:xe,useMap:null,value:$e,width:K,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:Ye,axis:null,background:null,bgColor:null,border:K,borderColor:null,bottomMargin:K,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:xe,declare:xe,event:null,face:null,frame:null,frameBorder:null,hSpace:K,leftMargin:K,link:null,longDesc:null,lowSrc:null,marginHeight:K,marginWidth:K,noResize:xe,noHref:xe,noShade:xe,noWrap:xe,object:null,profile:null,prompt:null,rev:null,rightMargin:K,rules:null,scheme:null,scrolling:$e,standby:null,summary:null,text:null,topMargin:K,valueType:null,version:null,vAlign:null,vLink:null,vSpace:K,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:xe,disableRemotePlayback:xe,prefix:null,property:null,results:K,security:null,unselectable:null},space:"html",transform:ay}),Z1=mi({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Lt,accentHeight:K,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:K,amplitude:K,arabicForm:null,ascent:K,attributeName:null,attributeType:null,azimuth:K,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:K,by:null,calcMode:null,capHeight:K,className:Ye,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:K,diffuseConstant:K,direction:null,display:null,dur:null,divisor:K,dominantBaseline:null,download:xe,dx:null,dy:null,edgeMode:null,editable:null,elevation:K,enableBackground:null,end:null,event:null,exponent:K,externalResourcesRequired:null,fill:null,fillOpacity:K,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:fi,g2:fi,glyphName:fi,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:K,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:K,horizOriginX:K,horizOriginY:K,id:null,ideographic:K,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:K,k:K,k1:K,k2:K,k3:K,k4:K,kernelMatrix:Lt,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:K,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:K,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:K,overlineThickness:K,paintOrder:null,panose1:null,path:null,pathLength:K,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:Ye,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:K,pointsAtY:K,pointsAtZ:K,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Lt,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Lt,rev:Lt,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Lt,requiredFeatures:Lt,requiredFonts:Lt,requiredFormats:Lt,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:K,specularExponent:K,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:K,strikethroughThickness:K,string:null,stroke:null,strokeDashArray:Lt,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:K,strokeOpacity:K,strokeWidth:null,style:null,surfaceScale:K,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Lt,tabIndex:K,tableValues:null,target:null,targetX:K,targetY:K,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Lt,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:K,underlineThickness:K,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:K,values:null,vAlphabetic:K,vMathematical:K,vectorEffect:null,vHanging:K,vIdeographic:K,version:null,vertAdvY:K,vertOriginX:K,vertOriginY:K,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:K,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:iy}),ry=mi({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(l,a){return"xlink:"+a.slice(5).toLowerCase()}}),uy=mi({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:ay}),oy=mi({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(l,a){return"xml:"+a.slice(3).toLowerCase()}}),K1={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},J1=/[A-Z]/g,Fp=/-[a-z]/g,F1=/^data[-\w.:]+$/i;function P1(l,a){const r=ic(a);let u=a,s=_t;if(r in l.normal)return l.property[l.normal[r]];if(r.length>4&&r.slice(0,4)==="data"&&F1.test(a)){if(a.charAt(4)==="-"){const f=a.slice(5).replace(Fp,$1);u="data"+f.charAt(0).toUpperCase()+f.slice(1)}else{const f=a.slice(4);if(!Fp.test(f)){let d=f.replace(J1,I1);d.charAt(0)!=="-"&&(d="-"+d),a="data"+d}}s=bc}return new s(u,a)}function I1(l){return"-"+l.toLowerCase()}function $1(l){return l.charAt(1).toUpperCase()}const W1=ny([ly,Q1,ry,uy,oy],"html"),xc=ny([ly,Z1,ry,uy,oy],"svg");function ex(l){return l.join(" ").trim()}var si={},qs,Pp;function tx(){if(Pp)return qs;Pp=1;var l=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,a=/\n/g,r=/^\s*/,u=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,s=/^:\s*/,f=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,d=/^[;\s]*/,h=/^\s+|\s+$/g,b=`
`,p="/",m="*",S="",T="comment",v="declaration";qs=function(U,C){if(typeof U!="string")throw new TypeError("First argument must be a string");if(!U)return[];C=C||{};var N=1,j=1;function H(re){var W=re.match(a);W&&(N+=W.length);var ne=re.lastIndexOf(b);j=~ne?re.length-ne:j+re.length}function P(){var re={line:N,column:j};return function(W){return W.position=new F(re),ye(),W}}function F(re){this.start=re,this.end={line:N,column:j},this.source=C.source}F.prototype.content=U;function q(re){var W=new Error(C.source+":"+N+":"+j+": "+re);if(W.reason=re,W.filename=C.source,W.line=N,W.column=j,W.source=U,!C.silent)throw W}function oe(re){var W=re.exec(U);if(W){var ne=W[0];return H(ne),U=U.slice(ne.length),W}}function ye(){oe(r)}function me(re){var W;for(re=re||[];W=Oe();)W!==!1&&re.push(W);return re}function Oe(){var re=P();if(!(p!=U.charAt(0)||m!=U.charAt(1))){for(var W=2;S!=U.charAt(W)&&(m!=U.charAt(W)||p!=U.charAt(W+1));)++W;if(W+=2,S===U.charAt(W-1))return q("End of comment missing");var ne=U.slice(2,W-2);return j+=2,H(ne),U=U.slice(W),j+=2,re({type:T,comment:ne})}}function ie(){var re=P(),W=oe(u);if(W){if(Oe(),!oe(s))return q("property missing ':'");var ne=oe(f),B=re({type:v,property:_(W[0].replace(l,S)),value:ne?_(ne[0].replace(l,S)):S});return oe(d),B}}function $(){var re=[];me(re);for(var W;W=ie();)W!==!1&&(re.push(W),me(re));return re}return ye(),$()};function _(U){return U?U.replace(h,S):S}return qs}var Ip;function nx(){if(Ip)return si;Ip=1;var l=si&&si.__importDefault||function(u){return u&&u.__esModule?u:{default:u}};Object.defineProperty(si,"__esModule",{value:!0}),si.default=r;var a=l(tx());function r(u,s){var f=null;if(!u||typeof u!="string")return f;var d=(0,a.default)(u),h=typeof s=="function";return d.forEach(function(b){if(b.type==="declaration"){var p=b.property,m=b.value;h?s(p,m,b):m&&(f=f||{},f[p]=m)}}),f}return si}var Sa={},$p;function lx(){if($p)return Sa;$p=1,Object.defineProperty(Sa,"__esModule",{value:!0}),Sa.camelCase=void 0;var l=/^--[a-zA-Z0-9_-]+$/,a=/-([a-z])/g,r=/^[^-]+$/,u=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,f=function(p){return!p||r.test(p)||l.test(p)},d=function(p,m){return m.toUpperCase()},h=function(p,m){return"".concat(m,"-")},b=function(p,m){return m===void 0&&(m={}),f(p)?p:(p=p.toLowerCase(),m.reactCompat?p=p.replace(s,h):p=p.replace(u,h),p.replace(a,d))};return Sa.camelCase=b,Sa}var va,Wp;function ix(){if(Wp)return va;Wp=1;var l=va&&va.__importDefault||function(s){return s&&s.__esModule?s:{default:s}},a=l(nx()),r=lx();function u(s,f){var d={};return!s||typeof s!="string"||(0,a.default)(s,function(h,b){h&&b&&(d[(0,r.camelCase)(h,f)]=b)}),d}return u.default=u,va=u,va}var ax=ix();const rx=Rm(ax),sy=cy("end"),Sc=cy("start");function cy(l){return a;function a(r){const u=r&&r.position&&r.position[l]||{};if(typeof u.line=="number"&&u.line>0&&typeof u.column=="number"&&u.column>0)return{line:u.line,column:u.column,offset:typeof u.offset=="number"&&u.offset>-1?u.offset:void 0}}}function ux(l){const a=Sc(l),r=sy(l);if(a&&r)return{start:a,end:r}}function Aa(l){return!l||typeof l!="object"?"":"position"in l||"type"in l?em(l.position):"start"in l||"end"in l?em(l):"line"in l||"column"in l?uc(l):""}function uc(l){return tm(l&&l.line)+":"+tm(l&&l.column)}function em(l){return uc(l&&l.start)+"-"+uc(l&&l.end)}function tm(l){return l&&typeof l=="number"?l:1}class pt extends Error{constructor(a,r,u){super(),typeof r=="string"&&(u=r,r=void 0);let s="",f={},d=!1;if(r&&("line"in r&&"column"in r?f={place:r}:"start"in r&&"end"in r?f={place:r}:"type"in r?f={ancestors:[r],place:r.position}:f={...r}),typeof a=="string"?s=a:!f.cause&&a&&(d=!0,s=a.message,f.cause=a),!f.ruleId&&!f.source&&typeof u=="string"){const b=u.indexOf(":");b===-1?f.ruleId=u:(f.source=u.slice(0,b),f.ruleId=u.slice(b+1))}if(!f.place&&f.ancestors&&f.ancestors){const b=f.ancestors[f.ancestors.length-1];b&&(f.place=b.position)}const h=f.place&&"start"in f.place?f.place.start:f.place;this.ancestors=f.ancestors||void 0,this.cause=f.cause||void 0,this.column=h?h.column:void 0,this.fatal=void 0,this.file="",this.message=s,this.line=h?h.line:void 0,this.name=Aa(f.place)||"1:1",this.place=f.place||void 0,this.reason=this.message,this.ruleId=f.ruleId||void 0,this.source=f.source||void 0,this.stack=d&&f.cause&&typeof f.cause.stack=="string"?f.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}pt.prototype.file="";pt.prototype.name="";pt.prototype.reason="";pt.prototype.message="";pt.prototype.stack="";pt.prototype.column=void 0;pt.prototype.line=void 0;pt.prototype.ancestors=void 0;pt.prototype.cause=void 0;pt.prototype.fatal=void 0;pt.prototype.place=void 0;pt.prototype.ruleId=void 0;pt.prototype.source=void 0;const vc={}.hasOwnProperty,ox=new Map,sx=/[A-Z]/g,cx=new Set(["table","tbody","thead","tfoot","tr"]),fx=new Set(["td","th"]),fy="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function dx(l,a){if(!a||a.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const r=a.filePath||void 0;let u;if(a.development){if(typeof a.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");u=Sx(r,a.jsxDEV)}else{if(typeof a.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof a.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");u=xx(r,a.jsx,a.jsxs)}const s={Fragment:a.Fragment,ancestors:[],components:a.components||{},create:u,elementAttributeNameCase:a.elementAttributeNameCase||"react",evaluater:a.createEvaluater?a.createEvaluater():void 0,filePath:r,ignoreInvalidStyle:a.ignoreInvalidStyle||!1,passKeys:a.passKeys!==!1,passNode:a.passNode||!1,schema:a.space==="svg"?xc:W1,stylePropertyNameCase:a.stylePropertyNameCase||"dom",tableCellAlignToStyle:a.tableCellAlignToStyle!==!1},f=dy(s,l,void 0);return f&&typeof f!="string"?f:s.create(l,s.Fragment,{children:f||void 0},void 0)}function dy(l,a,r){if(a.type==="element")return hx(l,a,r);if(a.type==="mdxFlowExpression"||a.type==="mdxTextExpression")return px(l,a);if(a.type==="mdxJsxFlowElement"||a.type==="mdxJsxTextElement")return yx(l,a,r);if(a.type==="mdxjsEsm")return mx(l,a);if(a.type==="root")return gx(l,a,r);if(a.type==="text")return bx(l,a)}function hx(l,a,r){const u=l.schema;let s=u;a.tagName.toLowerCase()==="svg"&&u.space==="html"&&(s=xc,l.schema=s),l.ancestors.push(a);const f=py(l,a.tagName,!1),d=vx(l,a);let h=Tc(l,a);return cx.has(a.tagName)&&(h=h.filter(function(b){return typeof b=="string"?!X1(b):!0})),hy(l,d,f,a),Ec(d,h),l.ancestors.pop(),l.schema=u,l.create(a,f,d,r)}function px(l,a){if(a.data&&a.data.estree&&l.evaluater){const u=a.data.estree.body[0];return u.type,l.evaluater.evaluateExpression(u.expression)}Ca(l,a.position)}function mx(l,a){if(a.data&&a.data.estree&&l.evaluater)return l.evaluater.evaluateProgram(a.data.estree);Ca(l,a.position)}function yx(l,a,r){const u=l.schema;let s=u;a.name==="svg"&&u.space==="html"&&(s=xc,l.schema=s),l.ancestors.push(a);const f=a.name===null?l.Fragment:py(l,a.name,!0),d=Ex(l,a),h=Tc(l,a);return hy(l,d,f,a),Ec(d,h),l.ancestors.pop(),l.schema=u,l.create(a,f,d,r)}function gx(l,a,r){const u={};return Ec(u,Tc(l,a)),l.create(a,l.Fragment,u,r)}function bx(l,a){return a.value}function hy(l,a,r,u){typeof r!="string"&&r!==l.Fragment&&l.passNode&&(a.node=u)}function Ec(l,a){if(a.length>0){const r=a.length>1?a:a[0];r&&(l.children=r)}}function xx(l,a,r){return u;function u(s,f,d,h){const p=Array.isArray(d.children)?r:a;return h?p(f,d,h):p(f,d)}}function Sx(l,a){return r;function r(u,s,f,d){const h=Array.isArray(f.children),b=Sc(u);return a(s,f,d,h,{columnNumber:b?b.column-1:void 0,fileName:l,lineNumber:b?b.line:void 0},void 0)}}function vx(l,a){const r={};let u,s;for(s in a.properties)if(s!=="children"&&vc.call(a.properties,s)){const f=Tx(l,s,a.properties[s]);if(f){const[d,h]=f;l.tableCellAlignToStyle&&d==="align"&&typeof h=="string"&&fx.has(a.tagName)?u=h:r[d]=h}}if(u){const f=r.style||(r.style={});f[l.stylePropertyNameCase==="css"?"text-align":"textAlign"]=u}return r}function Ex(l,a){const r={};for(const u of a.attributes)if(u.type==="mdxJsxExpressionAttribute")if(u.data&&u.data.estree&&l.evaluater){const f=u.data.estree.body[0];f.type;const d=f.expression;d.type;const h=d.properties[0];h.type,Object.assign(r,l.evaluater.evaluateExpression(h.argument))}else Ca(l,a.position);else{const s=u.name;let f;if(u.value&&typeof u.value=="object")if(u.value.data&&u.value.data.estree&&l.evaluater){const h=u.value.data.estree.body[0];h.type,f=l.evaluater.evaluateExpression(h.expression)}else Ca(l,a.position);else f=u.value===null?!0:u.value;r[s]=f}return r}function Tc(l,a){const r=[];let u=-1;const s=l.passKeys?new Map:ox;for(;++u<a.children.length;){const f=a.children[u];let d;if(l.passKeys){const b=f.type==="element"?f.tagName:f.type==="mdxJsxFlowElement"||f.type==="mdxJsxTextElement"?f.name:void 0;if(b){const p=s.get(b)||0;d=b+"-"+p,s.set(b,p+1)}}const h=dy(l,f,d);h!==void 0&&r.push(h)}return r}function Tx(l,a,r){const u=P1(l.schema,a);if(!(r==null||typeof r=="number"&&Number.isNaN(r))){if(Array.isArray(r)&&(r=u.commaSeparated?L1(r):ex(r)),u.property==="style"){let s=typeof r=="object"?r:Ax(l,String(r));return l.stylePropertyNameCase==="css"&&(s=wx(s)),["style",s]}return[l.elementAttributeNameCase==="react"&&u.space?K1[u.property]||u.property:u.attribute,r]}}function Ax(l,a){try{return rx(a,{reactCompat:!0})}catch(r){if(l.ignoreInvalidStyle)return{};const u=r,s=new pt("Cannot parse `style` attribute",{ancestors:l.ancestors,cause:u,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw s.file=l.filePath||void 0,s.url=fy+"#cannot-parse-style-attribute",s}}function py(l,a,r){let u;if(!r)u={type:"Literal",value:a};else if(a.includes(".")){const s=a.split(".");let f=-1,d;for(;++f<s.length;){const h=Zp(s[f])?{type:"Identifier",name:s[f]}:{type:"Literal",value:s[f]};d=d?{type:"MemberExpression",object:d,property:h,computed:!!(f&&h.type==="Literal"),optional:!1}:h}u=d}else u=Zp(a)&&!/^[a-z]/.test(a)?{type:"Identifier",name:a}:{type:"Literal",value:a};if(u.type==="Literal"){const s=u.value;return vc.call(l.components,s)?l.components[s]:s}if(l.evaluater)return l.evaluater.evaluateExpression(u);Ca(l)}function Ca(l,a){const r=new pt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:l.ancestors,place:a,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw r.file=l.filePath||void 0,r.url=fy+"#cannot-handle-mdx-estrees-without-createevaluater",r}function wx(l){const a={};let r;for(r in l)vc.call(l,r)&&(a[Ox(r)]=l[r]);return a}function Ox(l){let a=l.replace(sx,_x);return a.slice(0,3)==="ms-"&&(a="-"+a),a}function _x(l){return"-"+l.toLowerCase()}const Ys={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},Cx={};function Nx(l,a){const r=Cx,u=typeof r.includeImageAlt=="boolean"?r.includeImageAlt:!0,s=typeof r.includeHtml=="boolean"?r.includeHtml:!0;return my(l,u,s)}function my(l,a,r){if(Rx(l)){if("value"in l)return l.type==="html"&&!r?"":l.value;if(a&&"alt"in l&&l.alt)return l.alt;if("children"in l)return nm(l.children,a,r)}return Array.isArray(l)?nm(l,a,r):""}function nm(l,a,r){const u=[];let s=-1;for(;++s<l.length;)u[s]=my(l[s],a,r);return u.join("")}function Rx(l){return!!(l&&typeof l=="object")}const lm=document.createElement("i");function Ac(l){const a="&"+l+";";lm.innerHTML=a;const r=lm.textContent;return r.charCodeAt(r.length-1)===59&&l!=="semi"||r===a?!1:r}function hn(l,a,r,u){const s=l.length;let f=0,d;if(a<0?a=-a>s?0:s+a:a=a>s?s:a,r=r>0?r:0,u.length<1e4)d=Array.from(u),d.unshift(a,r),l.splice(...d);else for(r&&l.splice(a,r);f<u.length;)d=u.slice(f,f+1e4),d.unshift(a,0),l.splice(...d),f+=1e4,a+=1e4}function Pt(l,a){return l.length>0?(hn(l,l.length,0,a),l):a}const im={}.hasOwnProperty;function zx(l){const a={};let r=-1;for(;++r<l.length;)Dx(a,l[r]);return a}function Dx(l,a){let r;for(r in a){const s=(im.call(l,r)?l[r]:void 0)||(l[r]={}),f=a[r];let d;if(f)for(d in f){im.call(s,d)||(s[d]=[]);const h=f[d];kx(s[d],Array.isArray(h)?h:h?[h]:[])}}}function kx(l,a){let r=-1;const u=[];for(;++r<a.length;)(a[r].add==="after"?l:u).push(a[r]);hn(l,0,0,u)}function yy(l,a){const r=Number.parseInt(l,a);return r<9||r===11||r>13&&r<32||r>126&&r<160||r>55295&&r<57344||r>64975&&r<65008||(r&65535)===65535||(r&65535)===65534||r>1114111?"�":String.fromCodePoint(r)}function di(l){return l.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const dn=tl(/[A-Za-z]/),Ht=tl(/[\dA-Za-z]/),Mx=tl(/[#-'*+\--9=?A-Z^-~]/);function oc(l){return l!==null&&(l<32||l===127)}const sc=tl(/\d/),jx=tl(/[\dA-Fa-f]/),Ux=tl(/[!-/:-@[-`{-~]/);function pe(l){return l!==null&&l<-2}function Ot(l){return l!==null&&(l<0||l===32)}function ze(l){return l===-2||l===-1||l===32}const Bx=tl(new RegExp("\\p{P}|\\p{S}","u")),Lx=tl(/\s/);function tl(l){return a;function a(r){return r!==null&&r>-1&&l.test(String.fromCharCode(r))}}function yi(l){const a=[];let r=-1,u=0,s=0;for(;++r<l.length;){const f=l.charCodeAt(r);let d="";if(f===37&&Ht(l.charCodeAt(r+1))&&Ht(l.charCodeAt(r+2)))s=2;else if(f<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(f))||(d=String.fromCharCode(f));else if(f>55295&&f<57344){const h=l.charCodeAt(r+1);f<56320&&h>56319&&h<57344?(d=String.fromCharCode(f,h),s=1):d="�"}else d=String.fromCharCode(f);d&&(a.push(l.slice(u,r),encodeURIComponent(d)),u=r+s+1,d=""),s&&(r+=s,s=0)}return a.join("")+l.slice(u)}function Xe(l,a,r,u){const s=u?u-1:Number.POSITIVE_INFINITY;let f=0;return d;function d(b){return ze(b)?(l.enter(r),h(b)):a(b)}function h(b){return ze(b)&&f++<s?(l.consume(b),h):(l.exit(r),a(b))}}const Hx={tokenize:qx};function qx(l){const a=l.attempt(this.parser.constructs.contentInitial,u,s);let r;return a;function u(h){if(h===null){l.consume(h);return}return l.enter("lineEnding"),l.consume(h),l.exit("lineEnding"),Xe(l,a,"linePrefix")}function s(h){return l.enter("paragraph"),f(h)}function f(h){const b=l.enter("chunkText",{contentType:"text",previous:r});return r&&(r.next=b),r=b,d(h)}function d(h){if(h===null){l.exit("chunkText"),l.exit("paragraph"),l.consume(h);return}return pe(h)?(l.consume(h),l.exit("chunkText"),f):(l.consume(h),d)}}const Yx={tokenize:Vx},am={tokenize:Xx};function Vx(l){const a=this,r=[];let u=0,s,f,d;return h;function h(H){if(u<r.length){const P=r[u];return a.containerState=P[1],l.attempt(P[0].continuation,b,p)(H)}return p(H)}function b(H){if(u++,a.containerState._closeFlow){a.containerState._closeFlow=void 0,s&&j();const P=a.events.length;let F=P,q;for(;F--;)if(a.events[F][0]==="exit"&&a.events[F][1].type==="chunkFlow"){q=a.events[F][1].end;break}N(u);let oe=P;for(;oe<a.events.length;)a.events[oe][1].end={...q},oe++;return hn(a.events,F+1,0,a.events.slice(P)),a.events.length=oe,p(H)}return h(H)}function p(H){if(u===r.length){if(!s)return T(H);if(s.currentConstruct&&s.currentConstruct.concrete)return _(H);a.interrupt=!!(s.currentConstruct&&!s._gfmTableDynamicInterruptHack)}return a.containerState={},l.check(am,m,S)(H)}function m(H){return s&&j(),N(u),T(H)}function S(H){return a.parser.lazy[a.now().line]=u!==r.length,d=a.now().offset,_(H)}function T(H){return a.containerState={},l.attempt(am,v,_)(H)}function v(H){return u++,r.push([a.currentConstruct,a.containerState]),T(H)}function _(H){if(H===null){s&&j(),N(0),l.consume(H);return}return s=s||a.parser.flow(a.now()),l.enter("chunkFlow",{_tokenizer:s,contentType:"flow",previous:f}),U(H)}function U(H){if(H===null){C(l.exit("chunkFlow"),!0),N(0),l.consume(H);return}return pe(H)?(l.consume(H),C(l.exit("chunkFlow")),u=0,a.interrupt=void 0,h):(l.consume(H),U)}function C(H,P){const F=a.sliceStream(H);if(P&&F.push(null),H.previous=f,f&&(f.next=H),f=H,s.defineSkip(H.start),s.write(F),a.parser.lazy[H.start.line]){let q=s.events.length;for(;q--;)if(s.events[q][1].start.offset<d&&(!s.events[q][1].end||s.events[q][1].end.offset>d))return;const oe=a.events.length;let ye=oe,me,Oe;for(;ye--;)if(a.events[ye][0]==="exit"&&a.events[ye][1].type==="chunkFlow"){if(me){Oe=a.events[ye][1].end;break}me=!0}for(N(u),q=oe;q<a.events.length;)a.events[q][1].end={...Oe},q++;hn(a.events,ye+1,0,a.events.slice(oe)),a.events.length=q}}function N(H){let P=r.length;for(;P-- >H;){const F=r[P];a.containerState=F[1],F[0].exit.call(a,l)}r.length=H}function j(){s.write([null]),f=void 0,s=void 0,a.containerState._closeFlow=void 0}}function Xx(l,a,r){return Xe(l,l.attempt(this.parser.constructs.document,a,r),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function rm(l){if(l===null||Ot(l)||Lx(l))return 1;if(Bx(l))return 2}function wc(l,a,r){const u=[];let s=-1;for(;++s<l.length;){const f=l[s].resolveAll;f&&!u.includes(f)&&(a=f(a,r),u.push(f))}return a}const cc={name:"attention",resolveAll:Gx,tokenize:Qx};function Gx(l,a){let r=-1,u,s,f,d,h,b,p,m;for(;++r<l.length;)if(l[r][0]==="enter"&&l[r][1].type==="attentionSequence"&&l[r][1]._close){for(u=r;u--;)if(l[u][0]==="exit"&&l[u][1].type==="attentionSequence"&&l[u][1]._open&&a.sliceSerialize(l[u][1]).charCodeAt(0)===a.sliceSerialize(l[r][1]).charCodeAt(0)){if((l[u][1]._close||l[r][1]._open)&&(l[r][1].end.offset-l[r][1].start.offset)%3&&!((l[u][1].end.offset-l[u][1].start.offset+l[r][1].end.offset-l[r][1].start.offset)%3))continue;b=l[u][1].end.offset-l[u][1].start.offset>1&&l[r][1].end.offset-l[r][1].start.offset>1?2:1;const S={...l[u][1].end},T={...l[r][1].start};um(S,-b),um(T,b),d={type:b>1?"strongSequence":"emphasisSequence",start:S,end:{...l[u][1].end}},h={type:b>1?"strongSequence":"emphasisSequence",start:{...l[r][1].start},end:T},f={type:b>1?"strongText":"emphasisText",start:{...l[u][1].end},end:{...l[r][1].start}},s={type:b>1?"strong":"emphasis",start:{...d.start},end:{...h.end}},l[u][1].end={...d.start},l[r][1].start={...h.end},p=[],l[u][1].end.offset-l[u][1].start.offset&&(p=Pt(p,[["enter",l[u][1],a],["exit",l[u][1],a]])),p=Pt(p,[["enter",s,a],["enter",d,a],["exit",d,a],["enter",f,a]]),p=Pt(p,wc(a.parser.constructs.insideSpan.null,l.slice(u+1,r),a)),p=Pt(p,[["exit",f,a],["enter",h,a],["exit",h,a],["exit",s,a]]),l[r][1].end.offset-l[r][1].start.offset?(m=2,p=Pt(p,[["enter",l[r][1],a],["exit",l[r][1],a]])):m=0,hn(l,u-1,r-u+3,p),r=u+p.length-m-2;break}}for(r=-1;++r<l.length;)l[r][1].type==="attentionSequence"&&(l[r][1].type="data");return l}function Qx(l,a){const r=this.parser.constructs.attentionMarkers.null,u=this.previous,s=rm(u);let f;return d;function d(b){return f=b,l.enter("attentionSequence"),h(b)}function h(b){if(b===f)return l.consume(b),h;const p=l.exit("attentionSequence"),m=rm(b),S=!m||m===2&&s||r.includes(b),T=!s||s===2&&m||r.includes(u);return p._open=!!(f===42?S:S&&(s||!T)),p._close=!!(f===42?T:T&&(m||!S)),a(b)}}function um(l,a){l.column+=a,l.offset+=a,l._bufferIndex+=a}const Zx={name:"autolink",tokenize:Kx};function Kx(l,a,r){let u=0;return s;function s(v){return l.enter("autolink"),l.enter("autolinkMarker"),l.consume(v),l.exit("autolinkMarker"),l.enter("autolinkProtocol"),f}function f(v){return dn(v)?(l.consume(v),d):v===64?r(v):p(v)}function d(v){return v===43||v===45||v===46||Ht(v)?(u=1,h(v)):p(v)}function h(v){return v===58?(l.consume(v),u=0,b):(v===43||v===45||v===46||Ht(v))&&u++<32?(l.consume(v),h):(u=0,p(v))}function b(v){return v===62?(l.exit("autolinkProtocol"),l.enter("autolinkMarker"),l.consume(v),l.exit("autolinkMarker"),l.exit("autolink"),a):v===null||v===32||v===60||oc(v)?r(v):(l.consume(v),b)}function p(v){return v===64?(l.consume(v),m):Mx(v)?(l.consume(v),p):r(v)}function m(v){return Ht(v)?S(v):r(v)}function S(v){return v===46?(l.consume(v),u=0,m):v===62?(l.exit("autolinkProtocol").type="autolinkEmail",l.enter("autolinkMarker"),l.consume(v),l.exit("autolinkMarker"),l.exit("autolink"),a):T(v)}function T(v){if((v===45||Ht(v))&&u++<63){const _=v===45?T:S;return l.consume(v),_}return r(v)}}const fu={partial:!0,tokenize:Jx};function Jx(l,a,r){return u;function u(f){return ze(f)?Xe(l,s,"linePrefix")(f):s(f)}function s(f){return f===null||pe(f)?a(f):r(f)}}const gy={continuation:{tokenize:Px},exit:Ix,name:"blockQuote",tokenize:Fx};function Fx(l,a,r){const u=this;return s;function s(d){if(d===62){const h=u.containerState;return h.open||(l.enter("blockQuote",{_container:!0}),h.open=!0),l.enter("blockQuotePrefix"),l.enter("blockQuoteMarker"),l.consume(d),l.exit("blockQuoteMarker"),f}return r(d)}function f(d){return ze(d)?(l.enter("blockQuotePrefixWhitespace"),l.consume(d),l.exit("blockQuotePrefixWhitespace"),l.exit("blockQuotePrefix"),a):(l.exit("blockQuotePrefix"),a(d))}}function Px(l,a,r){const u=this;return s;function s(d){return ze(d)?Xe(l,f,"linePrefix",u.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(d):f(d)}function f(d){return l.attempt(gy,a,r)(d)}}function Ix(l){l.exit("blockQuote")}const by={name:"characterEscape",tokenize:$x};function $x(l,a,r){return u;function u(f){return l.enter("characterEscape"),l.enter("escapeMarker"),l.consume(f),l.exit("escapeMarker"),s}function s(f){return Ux(f)?(l.enter("characterEscapeValue"),l.consume(f),l.exit("characterEscapeValue"),l.exit("characterEscape"),a):r(f)}}const xy={name:"characterReference",tokenize:Wx};function Wx(l,a,r){const u=this;let s=0,f,d;return h;function h(S){return l.enter("characterReference"),l.enter("characterReferenceMarker"),l.consume(S),l.exit("characterReferenceMarker"),b}function b(S){return S===35?(l.enter("characterReferenceMarkerNumeric"),l.consume(S),l.exit("characterReferenceMarkerNumeric"),p):(l.enter("characterReferenceValue"),f=31,d=Ht,m(S))}function p(S){return S===88||S===120?(l.enter("characterReferenceMarkerHexadecimal"),l.consume(S),l.exit("characterReferenceMarkerHexadecimal"),l.enter("characterReferenceValue"),f=6,d=jx,m):(l.enter("characterReferenceValue"),f=7,d=sc,m(S))}function m(S){if(S===59&&s){const T=l.exit("characterReferenceValue");return d===Ht&&!Ac(u.sliceSerialize(T))?r(S):(l.enter("characterReferenceMarker"),l.consume(S),l.exit("characterReferenceMarker"),l.exit("characterReference"),a)}return d(S)&&s++<f?(l.consume(S),m):r(S)}}const om={partial:!0,tokenize:tS},sm={concrete:!0,name:"codeFenced",tokenize:eS};function eS(l,a,r){const u=this,s={partial:!0,tokenize:F};let f=0,d=0,h;return b;function b(q){return p(q)}function p(q){const oe=u.events[u.events.length-1];return f=oe&&oe[1].type==="linePrefix"?oe[2].sliceSerialize(oe[1],!0).length:0,h=q,l.enter("codeFenced"),l.enter("codeFencedFence"),l.enter("codeFencedFenceSequence"),m(q)}function m(q){return q===h?(d++,l.consume(q),m):d<3?r(q):(l.exit("codeFencedFenceSequence"),ze(q)?Xe(l,S,"whitespace")(q):S(q))}function S(q){return q===null||pe(q)?(l.exit("codeFencedFence"),u.interrupt?a(q):l.check(om,U,P)(q)):(l.enter("codeFencedFenceInfo"),l.enter("chunkString",{contentType:"string"}),T(q))}function T(q){return q===null||pe(q)?(l.exit("chunkString"),l.exit("codeFencedFenceInfo"),S(q)):ze(q)?(l.exit("chunkString"),l.exit("codeFencedFenceInfo"),Xe(l,v,"whitespace")(q)):q===96&&q===h?r(q):(l.consume(q),T)}function v(q){return q===null||pe(q)?S(q):(l.enter("codeFencedFenceMeta"),l.enter("chunkString",{contentType:"string"}),_(q))}function _(q){return q===null||pe(q)?(l.exit("chunkString"),l.exit("codeFencedFenceMeta"),S(q)):q===96&&q===h?r(q):(l.consume(q),_)}function U(q){return l.attempt(s,P,C)(q)}function C(q){return l.enter("lineEnding"),l.consume(q),l.exit("lineEnding"),N}function N(q){return f>0&&ze(q)?Xe(l,j,"linePrefix",f+1)(q):j(q)}function j(q){return q===null||pe(q)?l.check(om,U,P)(q):(l.enter("codeFlowValue"),H(q))}function H(q){return q===null||pe(q)?(l.exit("codeFlowValue"),j(q)):(l.consume(q),H)}function P(q){return l.exit("codeFenced"),a(q)}function F(q,oe,ye){let me=0;return Oe;function Oe(ne){return q.enter("lineEnding"),q.consume(ne),q.exit("lineEnding"),ie}function ie(ne){return q.enter("codeFencedFence"),ze(ne)?Xe(q,$,"linePrefix",u.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(ne):$(ne)}function $(ne){return ne===h?(q.enter("codeFencedFenceSequence"),re(ne)):ye(ne)}function re(ne){return ne===h?(me++,q.consume(ne),re):me>=d?(q.exit("codeFencedFenceSequence"),ze(ne)?Xe(q,W,"whitespace")(ne):W(ne)):ye(ne)}function W(ne){return ne===null||pe(ne)?(q.exit("codeFencedFence"),oe(ne)):ye(ne)}}}function tS(l,a,r){const u=this;return s;function s(d){return d===null?r(d):(l.enter("lineEnding"),l.consume(d),l.exit("lineEnding"),f)}function f(d){return u.parser.lazy[u.now().line]?r(d):a(d)}}const Vs={name:"codeIndented",tokenize:lS},nS={partial:!0,tokenize:iS};function lS(l,a,r){const u=this;return s;function s(p){return l.enter("codeIndented"),Xe(l,f,"linePrefix",5)(p)}function f(p){const m=u.events[u.events.length-1];return m&&m[1].type==="linePrefix"&&m[2].sliceSerialize(m[1],!0).length>=4?d(p):r(p)}function d(p){return p===null?b(p):pe(p)?l.attempt(nS,d,b)(p):(l.enter("codeFlowValue"),h(p))}function h(p){return p===null||pe(p)?(l.exit("codeFlowValue"),d(p)):(l.consume(p),h)}function b(p){return l.exit("codeIndented"),a(p)}}function iS(l,a,r){const u=this;return s;function s(d){return u.parser.lazy[u.now().line]?r(d):pe(d)?(l.enter("lineEnding"),l.consume(d),l.exit("lineEnding"),s):Xe(l,f,"linePrefix",5)(d)}function f(d){const h=u.events[u.events.length-1];return h&&h[1].type==="linePrefix"&&h[2].sliceSerialize(h[1],!0).length>=4?a(d):pe(d)?s(d):r(d)}}const aS={name:"codeText",previous:uS,resolve:rS,tokenize:oS};function rS(l){let a=l.length-4,r=3,u,s;if((l[r][1].type==="lineEnding"||l[r][1].type==="space")&&(l[a][1].type==="lineEnding"||l[a][1].type==="space")){for(u=r;++u<a;)if(l[u][1].type==="codeTextData"){l[r][1].type="codeTextPadding",l[a][1].type="codeTextPadding",r+=2,a-=2;break}}for(u=r-1,a++;++u<=a;)s===void 0?u!==a&&l[u][1].type!=="lineEnding"&&(s=u):(u===a||l[u][1].type==="lineEnding")&&(l[s][1].type="codeTextData",u!==s+2&&(l[s][1].end=l[u-1][1].end,l.splice(s+2,u-s-2),a-=u-s-2,u=s+2),s=void 0);return l}function uS(l){return l!==96||this.events[this.events.length-1][1].type==="characterEscape"}function oS(l,a,r){let u=0,s,f;return d;function d(S){return l.enter("codeText"),l.enter("codeTextSequence"),h(S)}function h(S){return S===96?(l.consume(S),u++,h):(l.exit("codeTextSequence"),b(S))}function b(S){return S===null?r(S):S===32?(l.enter("space"),l.consume(S),l.exit("space"),b):S===96?(f=l.enter("codeTextSequence"),s=0,m(S)):pe(S)?(l.enter("lineEnding"),l.consume(S),l.exit("lineEnding"),b):(l.enter("codeTextData"),p(S))}function p(S){return S===null||S===32||S===96||pe(S)?(l.exit("codeTextData"),b(S)):(l.consume(S),p)}function m(S){return S===96?(l.consume(S),s++,m):s===u?(l.exit("codeTextSequence"),l.exit("codeText"),a(S)):(f.type="codeTextData",p(S))}}class sS{constructor(a){this.left=a?[...a]:[],this.right=[]}get(a){if(a<0||a>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+a+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return a<this.left.length?this.left[a]:this.right[this.right.length-a+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(a,r){const u=r??Number.POSITIVE_INFINITY;return u<this.left.length?this.left.slice(a,u):a>this.left.length?this.right.slice(this.right.length-u+this.left.length,this.right.length-a+this.left.length).reverse():this.left.slice(a).concat(this.right.slice(this.right.length-u+this.left.length).reverse())}splice(a,r,u){const s=r||0;this.setCursor(Math.trunc(a));const f=this.right.splice(this.right.length-s,Number.POSITIVE_INFINITY);return u&&Ea(this.left,u),f.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(a){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(a)}pushMany(a){this.setCursor(Number.POSITIVE_INFINITY),Ea(this.left,a)}unshift(a){this.setCursor(0),this.right.push(a)}unshiftMany(a){this.setCursor(0),Ea(this.right,a.reverse())}setCursor(a){if(!(a===this.left.length||a>this.left.length&&this.right.length===0||a<0&&this.left.length===0))if(a<this.left.length){const r=this.left.splice(a,Number.POSITIVE_INFINITY);Ea(this.right,r.reverse())}else{const r=this.right.splice(this.left.length+this.right.length-a,Number.POSITIVE_INFINITY);Ea(this.left,r.reverse())}}}function Ea(l,a){let r=0;if(a.length<1e4)l.push(...a);else for(;r<a.length;)l.push(...a.slice(r,r+1e4)),r+=1e4}function Sy(l){const a={};let r=-1,u,s,f,d,h,b,p;const m=new sS(l);for(;++r<m.length;){for(;r in a;)r=a[r];if(u=m.get(r),r&&u[1].type==="chunkFlow"&&m.get(r-1)[1].type==="listItemPrefix"&&(b=u[1]._tokenizer.events,f=0,f<b.length&&b[f][1].type==="lineEndingBlank"&&(f+=2),f<b.length&&b[f][1].type==="content"))for(;++f<b.length&&b[f][1].type!=="content";)b[f][1].type==="chunkText"&&(b[f][1]._isInFirstContentOfListItem=!0,f++);if(u[0]==="enter")u[1].contentType&&(Object.assign(a,cS(m,r)),r=a[r],p=!0);else if(u[1]._container){for(f=r,s=void 0;f--;)if(d=m.get(f),d[1].type==="lineEnding"||d[1].type==="lineEndingBlank")d[0]==="enter"&&(s&&(m.get(s)[1].type="lineEndingBlank"),d[1].type="lineEnding",s=f);else if(!(d[1].type==="linePrefix"||d[1].type==="listItemIndent"))break;s&&(u[1].end={...m.get(s)[1].start},h=m.slice(s,r),h.unshift(u),m.splice(s,r-s+1,h))}}return hn(l,0,Number.POSITIVE_INFINITY,m.slice(0)),!p}function cS(l,a){const r=l.get(a)[1],u=l.get(a)[2];let s=a-1;const f=[];let d=r._tokenizer;d||(d=u.parser[r.contentType](r.start),r._contentTypeTextTrailing&&(d._contentTypeTextTrailing=!0));const h=d.events,b=[],p={};let m,S,T=-1,v=r,_=0,U=0;const C=[U];for(;v;){for(;l.get(++s)[1]!==v;);f.push(s),v._tokenizer||(m=u.sliceStream(v),v.next||m.push(null),S&&d.defineSkip(v.start),v._isInFirstContentOfListItem&&(d._gfmTasklistFirstContentOfListItem=!0),d.write(m),v._isInFirstContentOfListItem&&(d._gfmTasklistFirstContentOfListItem=void 0)),S=v,v=v.next}for(v=r;++T<h.length;)h[T][0]==="exit"&&h[T-1][0]==="enter"&&h[T][1].type===h[T-1][1].type&&h[T][1].start.line!==h[T][1].end.line&&(U=T+1,C.push(U),v._tokenizer=void 0,v.previous=void 0,v=v.next);for(d.events=[],v?(v._tokenizer=void 0,v.previous=void 0):C.pop(),T=C.length;T--;){const N=h.slice(C[T],C[T+1]),j=f.pop();b.push([j,j+N.length-1]),l.splice(j,2,N)}for(b.reverse(),T=-1;++T<b.length;)p[_+b[T][0]]=_+b[T][1],_+=b[T][1]-b[T][0]-1;return p}const fS={resolve:hS,tokenize:pS},dS={partial:!0,tokenize:mS};function hS(l){return Sy(l),l}function pS(l,a){let r;return u;function u(h){return l.enter("content"),r=l.enter("chunkContent",{contentType:"content"}),s(h)}function s(h){return h===null?f(h):pe(h)?l.check(dS,d,f)(h):(l.consume(h),s)}function f(h){return l.exit("chunkContent"),l.exit("content"),a(h)}function d(h){return l.consume(h),l.exit("chunkContent"),r.next=l.enter("chunkContent",{contentType:"content",previous:r}),r=r.next,s}}function mS(l,a,r){const u=this;return s;function s(d){return l.exit("chunkContent"),l.enter("lineEnding"),l.consume(d),l.exit("lineEnding"),Xe(l,f,"linePrefix")}function f(d){if(d===null||pe(d))return r(d);const h=u.events[u.events.length-1];return!u.parser.constructs.disable.null.includes("codeIndented")&&h&&h[1].type==="linePrefix"&&h[2].sliceSerialize(h[1],!0).length>=4?a(d):l.interrupt(u.parser.constructs.flow,r,a)(d)}}function vy(l,a,r,u,s,f,d,h,b){const p=b||Number.POSITIVE_INFINITY;let m=0;return S;function S(N){return N===60?(l.enter(u),l.enter(s),l.enter(f),l.consume(N),l.exit(f),T):N===null||N===32||N===41||oc(N)?r(N):(l.enter(u),l.enter(d),l.enter(h),l.enter("chunkString",{contentType:"string"}),U(N))}function T(N){return N===62?(l.enter(f),l.consume(N),l.exit(f),l.exit(s),l.exit(u),a):(l.enter(h),l.enter("chunkString",{contentType:"string"}),v(N))}function v(N){return N===62?(l.exit("chunkString"),l.exit(h),T(N)):N===null||N===60||pe(N)?r(N):(l.consume(N),N===92?_:v)}function _(N){return N===60||N===62||N===92?(l.consume(N),v):v(N)}function U(N){return!m&&(N===null||N===41||Ot(N))?(l.exit("chunkString"),l.exit(h),l.exit(d),l.exit(u),a(N)):m<p&&N===40?(l.consume(N),m++,U):N===41?(l.consume(N),m--,U):N===null||N===32||N===40||oc(N)?r(N):(l.consume(N),N===92?C:U)}function C(N){return N===40||N===41||N===92?(l.consume(N),U):U(N)}}function Ey(l,a,r,u,s,f){const d=this;let h=0,b;return p;function p(v){return l.enter(u),l.enter(s),l.consume(v),l.exit(s),l.enter(f),m}function m(v){return h>999||v===null||v===91||v===93&&!b||v===94&&!h&&"_hiddenFootnoteSupport"in d.parser.constructs?r(v):v===93?(l.exit(f),l.enter(s),l.consume(v),l.exit(s),l.exit(u),a):pe(v)?(l.enter("lineEnding"),l.consume(v),l.exit("lineEnding"),m):(l.enter("chunkString",{contentType:"string"}),S(v))}function S(v){return v===null||v===91||v===93||pe(v)||h++>999?(l.exit("chunkString"),m(v)):(l.consume(v),b||(b=!ze(v)),v===92?T:S)}function T(v){return v===91||v===92||v===93?(l.consume(v),h++,S):S(v)}}function Ty(l,a,r,u,s,f){let d;return h;function h(T){return T===34||T===39||T===40?(l.enter(u),l.enter(s),l.consume(T),l.exit(s),d=T===40?41:T,b):r(T)}function b(T){return T===d?(l.enter(s),l.consume(T),l.exit(s),l.exit(u),a):(l.enter(f),p(T))}function p(T){return T===d?(l.exit(f),b(d)):T===null?r(T):pe(T)?(l.enter("lineEnding"),l.consume(T),l.exit("lineEnding"),Xe(l,p,"linePrefix")):(l.enter("chunkString",{contentType:"string"}),m(T))}function m(T){return T===d||T===null||pe(T)?(l.exit("chunkString"),p(T)):(l.consume(T),T===92?S:m)}function S(T){return T===d||T===92?(l.consume(T),m):m(T)}}function wa(l,a){let r;return u;function u(s){return pe(s)?(l.enter("lineEnding"),l.consume(s),l.exit("lineEnding"),r=!0,u):ze(s)?Xe(l,u,r?"linePrefix":"lineSuffix")(s):a(s)}}const yS={name:"definition",tokenize:bS},gS={partial:!0,tokenize:xS};function bS(l,a,r){const u=this;let s;return f;function f(v){return l.enter("definition"),d(v)}function d(v){return Ey.call(u,l,h,r,"definitionLabel","definitionLabelMarker","definitionLabelString")(v)}function h(v){return s=di(u.sliceSerialize(u.events[u.events.length-1][1]).slice(1,-1)),v===58?(l.enter("definitionMarker"),l.consume(v),l.exit("definitionMarker"),b):r(v)}function b(v){return Ot(v)?wa(l,p)(v):p(v)}function p(v){return vy(l,m,r,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(v)}function m(v){return l.attempt(gS,S,S)(v)}function S(v){return ze(v)?Xe(l,T,"whitespace")(v):T(v)}function T(v){return v===null||pe(v)?(l.exit("definition"),u.parser.defined.push(s),a(v)):r(v)}}function xS(l,a,r){return u;function u(h){return Ot(h)?wa(l,s)(h):r(h)}function s(h){return Ty(l,f,r,"definitionTitle","definitionTitleMarker","definitionTitleString")(h)}function f(h){return ze(h)?Xe(l,d,"whitespace")(h):d(h)}function d(h){return h===null||pe(h)?a(h):r(h)}}const SS={name:"hardBreakEscape",tokenize:vS};function vS(l,a,r){return u;function u(f){return l.enter("hardBreakEscape"),l.consume(f),s}function s(f){return pe(f)?(l.exit("hardBreakEscape"),a(f)):r(f)}}const ES={name:"headingAtx",resolve:TS,tokenize:AS};function TS(l,a){let r=l.length-2,u=3,s,f;return l[u][1].type==="whitespace"&&(u+=2),r-2>u&&l[r][1].type==="whitespace"&&(r-=2),l[r][1].type==="atxHeadingSequence"&&(u===r-1||r-4>u&&l[r-2][1].type==="whitespace")&&(r-=u+1===r?2:4),r>u&&(s={type:"atxHeadingText",start:l[u][1].start,end:l[r][1].end},f={type:"chunkText",start:l[u][1].start,end:l[r][1].end,contentType:"text"},hn(l,u,r-u+1,[["enter",s,a],["enter",f,a],["exit",f,a],["exit",s,a]])),l}function AS(l,a,r){let u=0;return s;function s(m){return l.enter("atxHeading"),f(m)}function f(m){return l.enter("atxHeadingSequence"),d(m)}function d(m){return m===35&&u++<6?(l.consume(m),d):m===null||Ot(m)?(l.exit("atxHeadingSequence"),h(m)):r(m)}function h(m){return m===35?(l.enter("atxHeadingSequence"),b(m)):m===null||pe(m)?(l.exit("atxHeading"),a(m)):ze(m)?Xe(l,h,"whitespace")(m):(l.enter("atxHeadingText"),p(m))}function b(m){return m===35?(l.consume(m),b):(l.exit("atxHeadingSequence"),h(m))}function p(m){return m===null||m===35||Ot(m)?(l.exit("atxHeadingText"),h(m)):(l.consume(m),p)}}const wS=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],cm=["pre","script","style","textarea"],OS={concrete:!0,name:"htmlFlow",resolveTo:NS,tokenize:RS},_S={partial:!0,tokenize:DS},CS={partial:!0,tokenize:zS};function NS(l){let a=l.length;for(;a--&&!(l[a][0]==="enter"&&l[a][1].type==="htmlFlow"););return a>1&&l[a-2][1].type==="linePrefix"&&(l[a][1].start=l[a-2][1].start,l[a+1][1].start=l[a-2][1].start,l.splice(a-2,2)),l}function RS(l,a,r){const u=this;let s,f,d,h,b;return p;function p(E){return m(E)}function m(E){return l.enter("htmlFlow"),l.enter("htmlFlowData"),l.consume(E),S}function S(E){return E===33?(l.consume(E),T):E===47?(l.consume(E),f=!0,U):E===63?(l.consume(E),s=3,u.interrupt?a:g):dn(E)?(l.consume(E),d=String.fromCharCode(E),C):r(E)}function T(E){return E===45?(l.consume(E),s=2,v):E===91?(l.consume(E),s=5,h=0,_):dn(E)?(l.consume(E),s=4,u.interrupt?a:g):r(E)}function v(E){return E===45?(l.consume(E),u.interrupt?a:g):r(E)}function _(E){const te="CDATA[";return E===te.charCodeAt(h++)?(l.consume(E),h===te.length?u.interrupt?a:$:_):r(E)}function U(E){return dn(E)?(l.consume(E),d=String.fromCharCode(E),C):r(E)}function C(E){if(E===null||E===47||E===62||Ot(E)){const te=E===47,Se=d.toLowerCase();return!te&&!f&&cm.includes(Se)?(s=1,u.interrupt?a(E):$(E)):wS.includes(d.toLowerCase())?(s=6,te?(l.consume(E),N):u.interrupt?a(E):$(E)):(s=7,u.interrupt&&!u.parser.lazy[u.now().line]?r(E):f?j(E):H(E))}return E===45||Ht(E)?(l.consume(E),d+=String.fromCharCode(E),C):r(E)}function N(E){return E===62?(l.consume(E),u.interrupt?a:$):r(E)}function j(E){return ze(E)?(l.consume(E),j):Oe(E)}function H(E){return E===47?(l.consume(E),Oe):E===58||E===95||dn(E)?(l.consume(E),P):ze(E)?(l.consume(E),H):Oe(E)}function P(E){return E===45||E===46||E===58||E===95||Ht(E)?(l.consume(E),P):F(E)}function F(E){return E===61?(l.consume(E),q):ze(E)?(l.consume(E),F):H(E)}function q(E){return E===null||E===60||E===61||E===62||E===96?r(E):E===34||E===39?(l.consume(E),b=E,oe):ze(E)?(l.consume(E),q):ye(E)}function oe(E){return E===b?(l.consume(E),b=null,me):E===null||pe(E)?r(E):(l.consume(E),oe)}function ye(E){return E===null||E===34||E===39||E===47||E===60||E===61||E===62||E===96||Ot(E)?F(E):(l.consume(E),ye)}function me(E){return E===47||E===62||ze(E)?H(E):r(E)}function Oe(E){return E===62?(l.consume(E),ie):r(E)}function ie(E){return E===null||pe(E)?$(E):ze(E)?(l.consume(E),ie):r(E)}function $(E){return E===45&&s===2?(l.consume(E),B):E===60&&s===1?(l.consume(E),J):E===62&&s===4?(l.consume(E),G):E===63&&s===3?(l.consume(E),g):E===93&&s===5?(l.consume(E),Te):pe(E)&&(s===6||s===7)?(l.exit("htmlFlowData"),l.check(_S,I,re)(E)):E===null||pe(E)?(l.exit("htmlFlowData"),re(E)):(l.consume(E),$)}function re(E){return l.check(CS,W,I)(E)}function W(E){return l.enter("lineEnding"),l.consume(E),l.exit("lineEnding"),ne}function ne(E){return E===null||pe(E)?re(E):(l.enter("htmlFlowData"),$(E))}function B(E){return E===45?(l.consume(E),g):$(E)}function J(E){return E===47?(l.consume(E),d="",ae):$(E)}function ae(E){if(E===62){const te=d.toLowerCase();return cm.includes(te)?(l.consume(E),G):$(E)}return dn(E)&&d.length<8?(l.consume(E),d+=String.fromCharCode(E),ae):$(E)}function Te(E){return E===93?(l.consume(E),g):$(E)}function g(E){return E===62?(l.consume(E),G):E===45&&s===2?(l.consume(E),g):$(E)}function G(E){return E===null||pe(E)?(l.exit("htmlFlowData"),I(E)):(l.consume(E),G)}function I(E){return l.exit("htmlFlow"),a(E)}}function zS(l,a,r){const u=this;return s;function s(d){return pe(d)?(l.enter("lineEnding"),l.consume(d),l.exit("lineEnding"),f):r(d)}function f(d){return u.parser.lazy[u.now().line]?r(d):a(d)}}function DS(l,a,r){return u;function u(s){return l.enter("lineEnding"),l.consume(s),l.exit("lineEnding"),l.attempt(fu,a,r)}}const kS={name:"htmlText",tokenize:MS};function MS(l,a,r){const u=this;let s,f,d;return h;function h(g){return l.enter("htmlText"),l.enter("htmlTextData"),l.consume(g),b}function b(g){return g===33?(l.consume(g),p):g===47?(l.consume(g),F):g===63?(l.consume(g),H):dn(g)?(l.consume(g),ye):r(g)}function p(g){return g===45?(l.consume(g),m):g===91?(l.consume(g),f=0,_):dn(g)?(l.consume(g),j):r(g)}function m(g){return g===45?(l.consume(g),v):r(g)}function S(g){return g===null?r(g):g===45?(l.consume(g),T):pe(g)?(d=S,J(g)):(l.consume(g),S)}function T(g){return g===45?(l.consume(g),v):S(g)}function v(g){return g===62?B(g):g===45?T(g):S(g)}function _(g){const G="CDATA[";return g===G.charCodeAt(f++)?(l.consume(g),f===G.length?U:_):r(g)}function U(g){return g===null?r(g):g===93?(l.consume(g),C):pe(g)?(d=U,J(g)):(l.consume(g),U)}function C(g){return g===93?(l.consume(g),N):U(g)}function N(g){return g===62?B(g):g===93?(l.consume(g),N):U(g)}function j(g){return g===null||g===62?B(g):pe(g)?(d=j,J(g)):(l.consume(g),j)}function H(g){return g===null?r(g):g===63?(l.consume(g),P):pe(g)?(d=H,J(g)):(l.consume(g),H)}function P(g){return g===62?B(g):H(g)}function F(g){return dn(g)?(l.consume(g),q):r(g)}function q(g){return g===45||Ht(g)?(l.consume(g),q):oe(g)}function oe(g){return pe(g)?(d=oe,J(g)):ze(g)?(l.consume(g),oe):B(g)}function ye(g){return g===45||Ht(g)?(l.consume(g),ye):g===47||g===62||Ot(g)?me(g):r(g)}function me(g){return g===47?(l.consume(g),B):g===58||g===95||dn(g)?(l.consume(g),Oe):pe(g)?(d=me,J(g)):ze(g)?(l.consume(g),me):B(g)}function Oe(g){return g===45||g===46||g===58||g===95||Ht(g)?(l.consume(g),Oe):ie(g)}function ie(g){return g===61?(l.consume(g),$):pe(g)?(d=ie,J(g)):ze(g)?(l.consume(g),ie):me(g)}function $(g){return g===null||g===60||g===61||g===62||g===96?r(g):g===34||g===39?(l.consume(g),s=g,re):pe(g)?(d=$,J(g)):ze(g)?(l.consume(g),$):(l.consume(g),W)}function re(g){return g===s?(l.consume(g),s=void 0,ne):g===null?r(g):pe(g)?(d=re,J(g)):(l.consume(g),re)}function W(g){return g===null||g===34||g===39||g===60||g===61||g===96?r(g):g===47||g===62||Ot(g)?me(g):(l.consume(g),W)}function ne(g){return g===47||g===62||Ot(g)?me(g):r(g)}function B(g){return g===62?(l.consume(g),l.exit("htmlTextData"),l.exit("htmlText"),a):r(g)}function J(g){return l.exit("htmlTextData"),l.enter("lineEnding"),l.consume(g),l.exit("lineEnding"),ae}function ae(g){return ze(g)?Xe(l,Te,"linePrefix",u.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(g):Te(g)}function Te(g){return l.enter("htmlTextData"),d(g)}}const Oc={name:"labelEnd",resolveAll:LS,resolveTo:HS,tokenize:qS},jS={tokenize:YS},US={tokenize:VS},BS={tokenize:XS};function LS(l){let a=-1;const r=[];for(;++a<l.length;){const u=l[a][1];if(r.push(l[a]),u.type==="labelImage"||u.type==="labelLink"||u.type==="labelEnd"){const s=u.type==="labelImage"?4:2;u.type="data",a+=s}}return l.length!==r.length&&hn(l,0,l.length,r),l}function HS(l,a){let r=l.length,u=0,s,f,d,h;for(;r--;)if(s=l[r][1],f){if(s.type==="link"||s.type==="labelLink"&&s._inactive)break;l[r][0]==="enter"&&s.type==="labelLink"&&(s._inactive=!0)}else if(d){if(l[r][0]==="enter"&&(s.type==="labelImage"||s.type==="labelLink")&&!s._balanced&&(f=r,s.type!=="labelLink")){u=2;break}}else s.type==="labelEnd"&&(d=r);const b={type:l[f][1].type==="labelLink"?"link":"image",start:{...l[f][1].start},end:{...l[l.length-1][1].end}},p={type:"label",start:{...l[f][1].start},end:{...l[d][1].end}},m={type:"labelText",start:{...l[f+u+2][1].end},end:{...l[d-2][1].start}};return h=[["enter",b,a],["enter",p,a]],h=Pt(h,l.slice(f+1,f+u+3)),h=Pt(h,[["enter",m,a]]),h=Pt(h,wc(a.parser.constructs.insideSpan.null,l.slice(f+u+4,d-3),a)),h=Pt(h,[["exit",m,a],l[d-2],l[d-1],["exit",p,a]]),h=Pt(h,l.slice(d+1)),h=Pt(h,[["exit",b,a]]),hn(l,f,l.length,h),l}function qS(l,a,r){const u=this;let s=u.events.length,f,d;for(;s--;)if((u.events[s][1].type==="labelImage"||u.events[s][1].type==="labelLink")&&!u.events[s][1]._balanced){f=u.events[s][1];break}return h;function h(T){return f?f._inactive?S(T):(d=u.parser.defined.includes(di(u.sliceSerialize({start:f.end,end:u.now()}))),l.enter("labelEnd"),l.enter("labelMarker"),l.consume(T),l.exit("labelMarker"),l.exit("labelEnd"),b):r(T)}function b(T){return T===40?l.attempt(jS,m,d?m:S)(T):T===91?l.attempt(US,m,d?p:S)(T):d?m(T):S(T)}function p(T){return l.attempt(BS,m,S)(T)}function m(T){return a(T)}function S(T){return f._balanced=!0,r(T)}}function YS(l,a,r){return u;function u(S){return l.enter("resource"),l.enter("resourceMarker"),l.consume(S),l.exit("resourceMarker"),s}function s(S){return Ot(S)?wa(l,f)(S):f(S)}function f(S){return S===41?m(S):vy(l,d,h,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(S)}function d(S){return Ot(S)?wa(l,b)(S):m(S)}function h(S){return r(S)}function b(S){return S===34||S===39||S===40?Ty(l,p,r,"resourceTitle","resourceTitleMarker","resourceTitleString")(S):m(S)}function p(S){return Ot(S)?wa(l,m)(S):m(S)}function m(S){return S===41?(l.enter("resourceMarker"),l.consume(S),l.exit("resourceMarker"),l.exit("resource"),a):r(S)}}function VS(l,a,r){const u=this;return s;function s(h){return Ey.call(u,l,f,d,"reference","referenceMarker","referenceString")(h)}function f(h){return u.parser.defined.includes(di(u.sliceSerialize(u.events[u.events.length-1][1]).slice(1,-1)))?a(h):r(h)}function d(h){return r(h)}}function XS(l,a,r){return u;function u(f){return l.enter("reference"),l.enter("referenceMarker"),l.consume(f),l.exit("referenceMarker"),s}function s(f){return f===93?(l.enter("referenceMarker"),l.consume(f),l.exit("referenceMarker"),l.exit("reference"),a):r(f)}}const GS={name:"labelStartImage",resolveAll:Oc.resolveAll,tokenize:QS};function QS(l,a,r){const u=this;return s;function s(h){return l.enter("labelImage"),l.enter("labelImageMarker"),l.consume(h),l.exit("labelImageMarker"),f}function f(h){return h===91?(l.enter("labelMarker"),l.consume(h),l.exit("labelMarker"),l.exit("labelImage"),d):r(h)}function d(h){return h===94&&"_hiddenFootnoteSupport"in u.parser.constructs?r(h):a(h)}}const ZS={name:"labelStartLink",resolveAll:Oc.resolveAll,tokenize:KS};function KS(l,a,r){const u=this;return s;function s(d){return l.enter("labelLink"),l.enter("labelMarker"),l.consume(d),l.exit("labelMarker"),l.exit("labelLink"),f}function f(d){return d===94&&"_hiddenFootnoteSupport"in u.parser.constructs?r(d):a(d)}}const Xs={name:"lineEnding",tokenize:JS};function JS(l,a){return r;function r(u){return l.enter("lineEnding"),l.consume(u),l.exit("lineEnding"),Xe(l,a,"linePrefix")}}const eu={name:"thematicBreak",tokenize:FS};function FS(l,a,r){let u=0,s;return f;function f(p){return l.enter("thematicBreak"),d(p)}function d(p){return s=p,h(p)}function h(p){return p===s?(l.enter("thematicBreakSequence"),b(p)):u>=3&&(p===null||pe(p))?(l.exit("thematicBreak"),a(p)):r(p)}function b(p){return p===s?(l.consume(p),u++,b):(l.exit("thematicBreakSequence"),ze(p)?Xe(l,h,"whitespace")(p):h(p))}}const Tt={continuation:{tokenize:WS},exit:tv,name:"list",tokenize:$S},PS={partial:!0,tokenize:nv},IS={partial:!0,tokenize:ev};function $S(l,a,r){const u=this,s=u.events[u.events.length-1];let f=s&&s[1].type==="linePrefix"?s[2].sliceSerialize(s[1],!0).length:0,d=0;return h;function h(v){const _=u.containerState.type||(v===42||v===43||v===45?"listUnordered":"listOrdered");if(_==="listUnordered"?!u.containerState.marker||v===u.containerState.marker:sc(v)){if(u.containerState.type||(u.containerState.type=_,l.enter(_,{_container:!0})),_==="listUnordered")return l.enter("listItemPrefix"),v===42||v===45?l.check(eu,r,p)(v):p(v);if(!u.interrupt||v===49)return l.enter("listItemPrefix"),l.enter("listItemValue"),b(v)}return r(v)}function b(v){return sc(v)&&++d<10?(l.consume(v),b):(!u.interrupt||d<2)&&(u.containerState.marker?v===u.containerState.marker:v===41||v===46)?(l.exit("listItemValue"),p(v)):r(v)}function p(v){return l.enter("listItemMarker"),l.consume(v),l.exit("listItemMarker"),u.containerState.marker=u.containerState.marker||v,l.check(fu,u.interrupt?r:m,l.attempt(PS,T,S))}function m(v){return u.containerState.initialBlankLine=!0,f++,T(v)}function S(v){return ze(v)?(l.enter("listItemPrefixWhitespace"),l.consume(v),l.exit("listItemPrefixWhitespace"),T):r(v)}function T(v){return u.containerState.size=f+u.sliceSerialize(l.exit("listItemPrefix"),!0).length,a(v)}}function WS(l,a,r){const u=this;return u.containerState._closeFlow=void 0,l.check(fu,s,f);function s(h){return u.containerState.furtherBlankLines=u.containerState.furtherBlankLines||u.containerState.initialBlankLine,Xe(l,a,"listItemIndent",u.containerState.size+1)(h)}function f(h){return u.containerState.furtherBlankLines||!ze(h)?(u.containerState.furtherBlankLines=void 0,u.containerState.initialBlankLine=void 0,d(h)):(u.containerState.furtherBlankLines=void 0,u.containerState.initialBlankLine=void 0,l.attempt(IS,a,d)(h))}function d(h){return u.containerState._closeFlow=!0,u.interrupt=void 0,Xe(l,l.attempt(Tt,a,r),"linePrefix",u.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(h)}}function ev(l,a,r){const u=this;return Xe(l,s,"listItemIndent",u.containerState.size+1);function s(f){const d=u.events[u.events.length-1];return d&&d[1].type==="listItemIndent"&&d[2].sliceSerialize(d[1],!0).length===u.containerState.size?a(f):r(f)}}function tv(l){l.exit(this.containerState.type)}function nv(l,a,r){const u=this;return Xe(l,s,"listItemPrefixWhitespace",u.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function s(f){const d=u.events[u.events.length-1];return!ze(f)&&d&&d[1].type==="listItemPrefixWhitespace"?a(f):r(f)}}const fm={name:"setextUnderline",resolveTo:lv,tokenize:iv};function lv(l,a){let r=l.length,u,s,f;for(;r--;)if(l[r][0]==="enter"){if(l[r][1].type==="content"){u=r;break}l[r][1].type==="paragraph"&&(s=r)}else l[r][1].type==="content"&&l.splice(r,1),!f&&l[r][1].type==="definition"&&(f=r);const d={type:"setextHeading",start:{...l[u][1].start},end:{...l[l.length-1][1].end}};return l[s][1].type="setextHeadingText",f?(l.splice(s,0,["enter",d,a]),l.splice(f+1,0,["exit",l[u][1],a]),l[u][1].end={...l[f][1].end}):l[u][1]=d,l.push(["exit",d,a]),l}function iv(l,a,r){const u=this;let s;return f;function f(p){let m=u.events.length,S;for(;m--;)if(u.events[m][1].type!=="lineEnding"&&u.events[m][1].type!=="linePrefix"&&u.events[m][1].type!=="content"){S=u.events[m][1].type==="paragraph";break}return!u.parser.lazy[u.now().line]&&(u.interrupt||S)?(l.enter("setextHeadingLine"),s=p,d(p)):r(p)}function d(p){return l.enter("setextHeadingLineSequence"),h(p)}function h(p){return p===s?(l.consume(p),h):(l.exit("setextHeadingLineSequence"),ze(p)?Xe(l,b,"lineSuffix")(p):b(p))}function b(p){return p===null||pe(p)?(l.exit("setextHeadingLine"),a(p)):r(p)}}const av={tokenize:rv};function rv(l){const a=this,r=l.attempt(fu,u,l.attempt(this.parser.constructs.flowInitial,s,Xe(l,l.attempt(this.parser.constructs.flow,s,l.attempt(fS,s)),"linePrefix")));return r;function u(f){if(f===null){l.consume(f);return}return l.enter("lineEndingBlank"),l.consume(f),l.exit("lineEndingBlank"),a.currentConstruct=void 0,r}function s(f){if(f===null){l.consume(f);return}return l.enter("lineEnding"),l.consume(f),l.exit("lineEnding"),a.currentConstruct=void 0,r}}const uv={resolveAll:wy()},ov=Ay("string"),sv=Ay("text");function Ay(l){return{resolveAll:wy(l==="text"?cv:void 0),tokenize:a};function a(r){const u=this,s=this.parser.constructs[l],f=r.attempt(s,d,h);return d;function d(m){return p(m)?f(m):h(m)}function h(m){if(m===null){r.consume(m);return}return r.enter("data"),r.consume(m),b}function b(m){return p(m)?(r.exit("data"),f(m)):(r.consume(m),b)}function p(m){if(m===null)return!0;const S=s[m];let T=-1;if(S)for(;++T<S.length;){const v=S[T];if(!v.previous||v.previous.call(u,u.previous))return!0}return!1}}}function wy(l){return a;function a(r,u){let s=-1,f;for(;++s<=r.length;)f===void 0?r[s]&&r[s][1].type==="data"&&(f=s,s++):(!r[s]||r[s][1].type!=="data")&&(s!==f+2&&(r[f][1].end=r[s-1][1].end,r.splice(f+2,s-f-2),s=f+2),f=void 0);return l?l(r,u):r}}function cv(l,a){let r=0;for(;++r<=l.length;)if((r===l.length||l[r][1].type==="lineEnding")&&l[r-1][1].type==="data"){const u=l[r-1][1],s=a.sliceStream(u);let f=s.length,d=-1,h=0,b;for(;f--;){const p=s[f];if(typeof p=="string"){for(d=p.length;p.charCodeAt(d-1)===32;)h++,d--;if(d)break;d=-1}else if(p===-2)b=!0,h++;else if(p!==-1){f++;break}}if(a._contentTypeTextTrailing&&r===l.length&&(h=0),h){const p={type:r===l.length||b||h<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:f?d:u.start._bufferIndex+d,_index:u.start._index+f,line:u.end.line,column:u.end.column-h,offset:u.end.offset-h},end:{...u.end}};u.end={...p.start},u.start.offset===u.end.offset?Object.assign(u,p):(l.splice(r,0,["enter",p,a],["exit",p,a]),r+=2)}r++}return l}const fv={42:Tt,43:Tt,45:Tt,48:Tt,49:Tt,50:Tt,51:Tt,52:Tt,53:Tt,54:Tt,55:Tt,56:Tt,57:Tt,62:gy},dv={91:yS},hv={[-2]:Vs,[-1]:Vs,32:Vs},pv={35:ES,42:eu,45:[fm,eu],60:OS,61:fm,95:eu,96:sm,126:sm},mv={38:xy,92:by},yv={[-5]:Xs,[-4]:Xs,[-3]:Xs,33:GS,38:xy,42:cc,60:[Zx,kS],91:ZS,92:[SS,by],93:Oc,95:cc,96:aS},gv={null:[cc,uv]},bv={null:[42,95]},xv={null:[]},Sv=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:bv,contentInitial:dv,disable:xv,document:fv,flow:pv,flowInitial:hv,insideSpan:gv,string:mv,text:yv},Symbol.toStringTag,{value:"Module"}));function vv(l,a,r){let u={_bufferIndex:-1,_index:0,line:r&&r.line||1,column:r&&r.column||1,offset:r&&r.offset||0};const s={},f=[];let d=[],h=[];const b={attempt:oe(F),check:oe(q),consume:j,enter:H,exit:P,interrupt:oe(q,{interrupt:!0})},p={code:null,containerState:{},defineSkip:U,events:[],now:_,parser:l,previous:null,sliceSerialize:T,sliceStream:v,write:S};let m=a.tokenize.call(p,b);return a.resolveAll&&f.push(a),p;function S(ie){return d=Pt(d,ie),C(),d[d.length-1]!==null?[]:(ye(a,0),p.events=wc(f,p.events,p),p.events)}function T(ie,$){return Tv(v(ie),$)}function v(ie){return Ev(d,ie)}function _(){const{_bufferIndex:ie,_index:$,line:re,column:W,offset:ne}=u;return{_bufferIndex:ie,_index:$,line:re,column:W,offset:ne}}function U(ie){s[ie.line]=ie.column,Oe()}function C(){let ie;for(;u._index<d.length;){const $=d[u._index];if(typeof $=="string")for(ie=u._index,u._bufferIndex<0&&(u._bufferIndex=0);u._index===ie&&u._bufferIndex<$.length;)N($.charCodeAt(u._bufferIndex));else N($)}}function N(ie){m=m(ie)}function j(ie){pe(ie)?(u.line++,u.column=1,u.offset+=ie===-3?2:1,Oe()):ie!==-1&&(u.column++,u.offset++),u._bufferIndex<0?u._index++:(u._bufferIndex++,u._bufferIndex===d[u._index].length&&(u._bufferIndex=-1,u._index++)),p.previous=ie}function H(ie,$){const re=$||{};return re.type=ie,re.start=_(),p.events.push(["enter",re,p]),h.push(re),re}function P(ie){const $=h.pop();return $.end=_(),p.events.push(["exit",$,p]),$}function F(ie,$){ye(ie,$.from)}function q(ie,$){$.restore()}function oe(ie,$){return re;function re(W,ne,B){let J,ae,Te,g;return Array.isArray(W)?I(W):"tokenize"in W?I([W]):G(W);function G(le){return We;function We(Ce){const mt=Ce!==null&&le[Ce],nn=Ce!==null&&le.null,pn=[...Array.isArray(mt)?mt:mt?[mt]:[],...Array.isArray(nn)?nn:nn?[nn]:[]];return I(pn)(Ce)}}function I(le){return J=le,ae=0,le.length===0?B:E(le[ae])}function E(le){return We;function We(Ce){return g=me(),Te=le,le.partial||(p.currentConstruct=le),le.name&&p.parser.constructs.disable.null.includes(le.name)?Se():le.tokenize.call($?Object.assign(Object.create(p),$):p,b,te,Se)(Ce)}}function te(le){return ie(Te,g),ne}function Se(le){return g.restore(),++ae<J.length?E(J[ae]):B}}}function ye(ie,$){ie.resolveAll&&!f.includes(ie)&&f.push(ie),ie.resolve&&hn(p.events,$,p.events.length-$,ie.resolve(p.events.slice($),p)),ie.resolveTo&&(p.events=ie.resolveTo(p.events,p))}function me(){const ie=_(),$=p.previous,re=p.currentConstruct,W=p.events.length,ne=Array.from(h);return{from:W,restore:B};function B(){u=ie,p.previous=$,p.currentConstruct=re,p.events.length=W,h=ne,Oe()}}function Oe(){u.line in s&&u.column<2&&(u.column=s[u.line],u.offset+=s[u.line]-1)}}function Ev(l,a){const r=a.start._index,u=a.start._bufferIndex,s=a.end._index,f=a.end._bufferIndex;let d;if(r===s)d=[l[r].slice(u,f)];else{if(d=l.slice(r,s),u>-1){const h=d[0];typeof h=="string"?d[0]=h.slice(u):d.shift()}f>0&&d.push(l[s].slice(0,f))}return d}function Tv(l,a){let r=-1;const u=[];let s;for(;++r<l.length;){const f=l[r];let d;if(typeof f=="string")d=f;else switch(f){case-5:{d="\r";break}case-4:{d=`
`;break}case-3:{d=`\r
`;break}case-2:{d=a?" ":"	";break}case-1:{if(!a&&s)continue;d=" ";break}default:d=String.fromCharCode(f)}s=f===-2,u.push(d)}return u.join("")}function Av(l){const u={constructs:zx([Sv,...(l||{}).extensions||[]]),content:s(Hx),defined:[],document:s(Yx),flow:s(av),lazy:{},string:s(ov),text:s(sv)};return u;function s(f){return d;function d(h){return vv(u,f,h)}}}function wv(l){for(;!Sy(l););return l}const dm=/[\0\t\n\r]/g;function Ov(){let l=1,a="",r=!0,u;return s;function s(f,d,h){const b=[];let p,m,S,T,v;for(f=a+(typeof f=="string"?f.toString():new TextDecoder(d||void 0).decode(f)),S=0,a="",r&&(f.charCodeAt(0)===65279&&S++,r=void 0);S<f.length;){if(dm.lastIndex=S,p=dm.exec(f),T=p&&p.index!==void 0?p.index:f.length,v=f.charCodeAt(T),!p){a=f.slice(S);break}if(v===10&&S===T&&u)b.push(-3),u=void 0;else switch(u&&(b.push(-5),u=void 0),S<T&&(b.push(f.slice(S,T)),l+=T-S),v){case 0:{b.push(65533),l++;break}case 9:{for(m=Math.ceil(l/4)*4,b.push(-2);l++<m;)b.push(-1);break}case 10:{b.push(-4),l=1;break}default:u=!0,l=1}S=T+1}return h&&(u&&b.push(-5),a&&b.push(a),b.push(null)),b}}const _v=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Cv(l){return l.replace(_v,Nv)}function Nv(l,a,r){if(a)return a;if(r.charCodeAt(0)===35){const s=r.charCodeAt(1),f=s===120||s===88;return yy(r.slice(f?2:1),f?16:10)}return Ac(r)||l}const Oy={}.hasOwnProperty;function Rv(l,a,r){return typeof a!="string"&&(r=a,a=void 0),zv(r)(wv(Av(r).document().write(Ov()(l,a,!0))))}function zv(l){const a={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:f(Si),autolinkProtocol:me,autolinkEmail:me,atxHeading:f(ja),blockQuote:f(nn),characterEscape:me,characterReference:me,codeFenced:f(pn),codeFencedFenceInfo:d,codeFencedFenceMeta:d,codeIndented:f(pn,d),codeText:f(gi,d),codeTextData:me,data:me,codeFlowValue:me,definition:f(bi),definitionDestinationString:d,definitionLabelString:d,definitionTitleString:d,emphasis:f(xi),hardBreakEscape:f(Ua),hardBreakTrailing:f(Ua),htmlFlow:f(Ct,d),htmlFlowData:me,htmlText:f(Ct,d),htmlTextData:me,image:f(pu),label:d,link:f(Si),listItem:f(Tl),listItemValue:T,listOrdered:f(vi,S),listUnordered:f(vi),paragraph:f(mu),reference:E,referenceString:d,resourceDestinationString:d,resourceTitleString:d,setextHeading:f(ja),strong:f(Ba),thematicBreak:f(gu)},exit:{atxHeading:b(),atxHeadingSequence:F,autolink:b(),autolinkEmail:mt,autolinkProtocol:Ce,blockQuote:b(),characterEscapeValue:Oe,characterReferenceMarkerHexadecimal:Se,characterReferenceMarkerNumeric:Se,characterReferenceValue:le,characterReference:We,codeFenced:b(C),codeFencedFence:U,codeFencedFenceInfo:v,codeFencedFenceMeta:_,codeFlowValue:Oe,codeIndented:b(N),codeText:b(ne),codeTextData:Oe,data:Oe,definition:b(),definitionDestinationString:P,definitionLabelString:j,definitionTitleString:H,emphasis:b(),hardBreakEscape:b($),hardBreakTrailing:b($),htmlFlow:b(re),htmlFlowData:Oe,htmlText:b(W),htmlTextData:Oe,image:b(J),label:Te,labelText:ae,lineEnding:ie,link:b(B),listItem:b(),listOrdered:b(),listUnordered:b(),paragraph:b(),referenceString:te,resourceDestinationString:g,resourceTitleString:G,resource:I,setextHeading:b(ye),setextHeadingLineSequence:oe,setextHeadingText:q,strong:b(),thematicBreak:b()}};_y(a,(l||{}).mdastExtensions||[]);const r={};return u;function u(Y){let Z={type:"root",children:[]};const se={stack:[Z],tokenStack:[],config:a,enter:h,exit:p,buffer:d,resume:m,data:r},de=[];let De=-1;for(;++De<Y.length;)if(Y[De][1].type==="listOrdered"||Y[De][1].type==="listUnordered")if(Y[De][0]==="enter")de.push(De);else{const Nt=de.pop();De=s(Y,Nt,De)}for(De=-1;++De<Y.length;){const Nt=a[Y[De][0]];Oy.call(Nt,Y[De][1].type)&&Nt[Y[De][1].type].call(Object.assign({sliceSerialize:Y[De][2].sliceSerialize},se),Y[De][1])}if(se.tokenStack.length>0){const Nt=se.tokenStack[se.tokenStack.length-1];(Nt[1]||hm).call(se,void 0,Nt[0])}for(Z.position={start:el(Y.length>0?Y[0][1].start:{line:1,column:1,offset:0}),end:el(Y.length>0?Y[Y.length-2][1].end:{line:1,column:1,offset:0})},De=-1;++De<a.transforms.length;)Z=a.transforms[De](Z)||Z;return Z}function s(Y,Z,se){let de=Z-1,De=-1,Nt=!1,mn,gt,ln,Rt;for(;++de<=se;){const nt=Y[de];switch(nt[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{nt[0]==="enter"?De++:De--,Rt=void 0;break}case"lineEndingBlank":{nt[0]==="enter"&&(mn&&!Rt&&!De&&!ln&&(ln=de),Rt=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Rt=void 0}if(!De&&nt[0]==="enter"&&nt[1].type==="listItemPrefix"||De===-1&&nt[0]==="exit"&&(nt[1].type==="listUnordered"||nt[1].type==="listOrdered")){if(mn){let qt=de;for(gt=void 0;qt--;){const It=Y[qt];if(It[1].type==="lineEnding"||It[1].type==="lineEndingBlank"){if(It[0]==="exit")continue;gt&&(Y[gt][1].type="lineEndingBlank",Nt=!0),It[1].type="lineEnding",gt=qt}else if(!(It[1].type==="linePrefix"||It[1].type==="blockQuotePrefix"||It[1].type==="blockQuotePrefixWhitespace"||It[1].type==="blockQuoteMarker"||It[1].type==="listItemIndent"))break}ln&&(!gt||ln<gt)&&(mn._spread=!0),mn.end=Object.assign({},gt?Y[gt][1].start:nt[1].end),Y.splice(gt||de,0,["exit",mn,nt[2]]),de++,se++}if(nt[1].type==="listItemPrefix"){const qt={type:"listItem",_spread:!1,start:Object.assign({},nt[1].start),end:void 0};mn=qt,Y.splice(de,0,["enter",qt,nt[2]]),de++,se++,ln=void 0,Rt=!0}}}return Y[Z][1]._spread=Nt,se}function f(Y,Z){return se;function se(de){h.call(this,Y(de),de),Z&&Z.call(this,de)}}function d(){this.stack.push({type:"fragment",children:[]})}function h(Y,Z,se){this.stack[this.stack.length-1].children.push(Y),this.stack.push(Y),this.tokenStack.push([Z,se||void 0]),Y.position={start:el(Z.start),end:void 0}}function b(Y){return Z;function Z(se){Y&&Y.call(this,se),p.call(this,se)}}function p(Y,Z){const se=this.stack.pop(),de=this.tokenStack.pop();if(de)de[0].type!==Y.type&&(Z?Z.call(this,Y,de[0]):(de[1]||hm).call(this,Y,de[0]));else throw new Error("Cannot close `"+Y.type+"` ("+Aa({start:Y.start,end:Y.end})+"): it’s not open");se.position.end=el(Y.end)}function m(){return Nx(this.stack.pop())}function S(){this.data.expectingFirstListItemValue=!0}function T(Y){if(this.data.expectingFirstListItemValue){const Z=this.stack[this.stack.length-2];Z.start=Number.parseInt(this.sliceSerialize(Y),10),this.data.expectingFirstListItemValue=void 0}}function v(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.lang=Y}function _(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.meta=Y}function U(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function C(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.value=Y.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function N(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.value=Y.replace(/(\r?\n|\r)$/g,"")}function j(Y){const Z=this.resume(),se=this.stack[this.stack.length-1];se.label=Z,se.identifier=di(this.sliceSerialize(Y)).toLowerCase()}function H(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.title=Y}function P(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.url=Y}function F(Y){const Z=this.stack[this.stack.length-1];if(!Z.depth){const se=this.sliceSerialize(Y).length;Z.depth=se}}function q(){this.data.setextHeadingSlurpLineEnding=!0}function oe(Y){const Z=this.stack[this.stack.length-1];Z.depth=this.sliceSerialize(Y).codePointAt(0)===61?1:2}function ye(){this.data.setextHeadingSlurpLineEnding=void 0}function me(Y){const se=this.stack[this.stack.length-1].children;let de=se[se.length-1];(!de||de.type!=="text")&&(de=yu(),de.position={start:el(Y.start),end:void 0},se.push(de)),this.stack.push(de)}function Oe(Y){const Z=this.stack.pop();Z.value+=this.sliceSerialize(Y),Z.position.end=el(Y.end)}function ie(Y){const Z=this.stack[this.stack.length-1];if(this.data.atHardBreak){const se=Z.children[Z.children.length-1];se.position.end=el(Y.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&a.canContainEols.includes(Z.type)&&(me.call(this,Y),Oe.call(this,Y))}function $(){this.data.atHardBreak=!0}function re(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.value=Y}function W(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.value=Y}function ne(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.value=Y}function B(){const Y=this.stack[this.stack.length-1];if(this.data.inReference){const Z=this.data.referenceType||"shortcut";Y.type+="Reference",Y.referenceType=Z,delete Y.url,delete Y.title}else delete Y.identifier,delete Y.label;this.data.referenceType=void 0}function J(){const Y=this.stack[this.stack.length-1];if(this.data.inReference){const Z=this.data.referenceType||"shortcut";Y.type+="Reference",Y.referenceType=Z,delete Y.url,delete Y.title}else delete Y.identifier,delete Y.label;this.data.referenceType=void 0}function ae(Y){const Z=this.sliceSerialize(Y),se=this.stack[this.stack.length-2];se.label=Cv(Z),se.identifier=di(Z).toLowerCase()}function Te(){const Y=this.stack[this.stack.length-1],Z=this.resume(),se=this.stack[this.stack.length-1];if(this.data.inReference=!0,se.type==="link"){const de=Y.children;se.children=de}else se.alt=Z}function g(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.url=Y}function G(){const Y=this.resume(),Z=this.stack[this.stack.length-1];Z.title=Y}function I(){this.data.inReference=void 0}function E(){this.data.referenceType="collapsed"}function te(Y){const Z=this.resume(),se=this.stack[this.stack.length-1];se.label=Z,se.identifier=di(this.sliceSerialize(Y)).toLowerCase(),this.data.referenceType="full"}function Se(Y){this.data.characterReferenceType=Y.type}function le(Y){const Z=this.sliceSerialize(Y),se=this.data.characterReferenceType;let de;se?(de=yy(Z,se==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):de=Ac(Z);const De=this.stack[this.stack.length-1];De.value+=de}function We(Y){const Z=this.stack.pop();Z.position.end=el(Y.end)}function Ce(Y){Oe.call(this,Y);const Z=this.stack[this.stack.length-1];Z.url=this.sliceSerialize(Y)}function mt(Y){Oe.call(this,Y);const Z=this.stack[this.stack.length-1];Z.url="mailto:"+this.sliceSerialize(Y)}function nn(){return{type:"blockquote",children:[]}}function pn(){return{type:"code",lang:null,meta:null,value:""}}function gi(){return{type:"inlineCode",value:""}}function bi(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function xi(){return{type:"emphasis",children:[]}}function ja(){return{type:"heading",depth:0,children:[]}}function Ua(){return{type:"break"}}function Ct(){return{type:"html",value:""}}function pu(){return{type:"image",title:null,url:"",alt:null}}function Si(){return{type:"link",title:null,url:"",children:[]}}function vi(Y){return{type:"list",ordered:Y.type==="listOrdered",start:null,spread:Y._spread,children:[]}}function Tl(Y){return{type:"listItem",spread:Y._spread,checked:null,children:[]}}function mu(){return{type:"paragraph",children:[]}}function Ba(){return{type:"strong",children:[]}}function yu(){return{type:"text",value:""}}function gu(){return{type:"thematicBreak"}}}function el(l){return{line:l.line,column:l.column,offset:l.offset}}function _y(l,a){let r=-1;for(;++r<a.length;){const u=a[r];Array.isArray(u)?_y(l,u):Dv(l,u)}}function Dv(l,a){let r;for(r in a)if(Oy.call(a,r))switch(r){case"canContainEols":{const u=a[r];u&&l[r].push(...u);break}case"transforms":{const u=a[r];u&&l[r].push(...u);break}case"enter":case"exit":{const u=a[r];u&&Object.assign(l[r],u);break}}}function hm(l,a){throw l?new Error("Cannot close `"+l.type+"` ("+Aa({start:l.start,end:l.end})+"): a different token (`"+a.type+"`, "+Aa({start:a.start,end:a.end})+") is open"):new Error("Cannot close document, a token (`"+a.type+"`, "+Aa({start:a.start,end:a.end})+") is still open")}function kv(l){const a=this;a.parser=r;function r(u){return Rv(u,{...a.data("settings"),...l,extensions:a.data("micromarkExtensions")||[],mdastExtensions:a.data("fromMarkdownExtensions")||[]})}}function Mv(l,a){const r={type:"element",tagName:"blockquote",properties:{},children:l.wrap(l.all(a),!0)};return l.patch(a,r),l.applyData(a,r)}function jv(l,a){const r={type:"element",tagName:"br",properties:{},children:[]};return l.patch(a,r),[l.applyData(a,r),{type:"text",value:`
`}]}function Uv(l,a){const r=a.value?a.value+`
`:"",u={};a.lang&&(u.className=["language-"+a.lang]);let s={type:"element",tagName:"code",properties:u,children:[{type:"text",value:r}]};return a.meta&&(s.data={meta:a.meta}),l.patch(a,s),s=l.applyData(a,s),s={type:"element",tagName:"pre",properties:{},children:[s]},l.patch(a,s),s}function Bv(l,a){const r={type:"element",tagName:"del",properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}function Lv(l,a){const r={type:"element",tagName:"em",properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}function Hv(l,a){const r=typeof l.options.clobberPrefix=="string"?l.options.clobberPrefix:"user-content-",u=String(a.identifier).toUpperCase(),s=yi(u.toLowerCase()),f=l.footnoteOrder.indexOf(u);let d,h=l.footnoteCounts.get(u);h===void 0?(h=0,l.footnoteOrder.push(u),d=l.footnoteOrder.length):d=f+1,h+=1,l.footnoteCounts.set(u,h);const b={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+s,id:r+"fnref-"+s+(h>1?"-"+h:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(d)}]};l.patch(a,b);const p={type:"element",tagName:"sup",properties:{},children:[b]};return l.patch(a,p),l.applyData(a,p)}function qv(l,a){const r={type:"element",tagName:"h"+a.depth,properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}function Yv(l,a){if(l.options.allowDangerousHtml){const r={type:"raw",value:a.value};return l.patch(a,r),l.applyData(a,r)}}function Cy(l,a){const r=a.referenceType;let u="]";if(r==="collapsed"?u+="[]":r==="full"&&(u+="["+(a.label||a.identifier)+"]"),a.type==="imageReference")return[{type:"text",value:"!["+a.alt+u}];const s=l.all(a),f=s[0];f&&f.type==="text"?f.value="["+f.value:s.unshift({type:"text",value:"["});const d=s[s.length-1];return d&&d.type==="text"?d.value+=u:s.push({type:"text",value:u}),s}function Vv(l,a){const r=String(a.identifier).toUpperCase(),u=l.definitionById.get(r);if(!u)return Cy(l,a);const s={src:yi(u.url||""),alt:a.alt};u.title!==null&&u.title!==void 0&&(s.title=u.title);const f={type:"element",tagName:"img",properties:s,children:[]};return l.patch(a,f),l.applyData(a,f)}function Xv(l,a){const r={src:yi(a.url)};a.alt!==null&&a.alt!==void 0&&(r.alt=a.alt),a.title!==null&&a.title!==void 0&&(r.title=a.title);const u={type:"element",tagName:"img",properties:r,children:[]};return l.patch(a,u),l.applyData(a,u)}function Gv(l,a){const r={type:"text",value:a.value.replace(/\r?\n|\r/g," ")};l.patch(a,r);const u={type:"element",tagName:"code",properties:{},children:[r]};return l.patch(a,u),l.applyData(a,u)}function Qv(l,a){const r=String(a.identifier).toUpperCase(),u=l.definitionById.get(r);if(!u)return Cy(l,a);const s={href:yi(u.url||"")};u.title!==null&&u.title!==void 0&&(s.title=u.title);const f={type:"element",tagName:"a",properties:s,children:l.all(a)};return l.patch(a,f),l.applyData(a,f)}function Zv(l,a){const r={href:yi(a.url)};a.title!==null&&a.title!==void 0&&(r.title=a.title);const u={type:"element",tagName:"a",properties:r,children:l.all(a)};return l.patch(a,u),l.applyData(a,u)}function Kv(l,a,r){const u=l.all(a),s=r?Jv(r):Ny(a),f={},d=[];if(typeof a.checked=="boolean"){const m=u[0];let S;m&&m.type==="element"&&m.tagName==="p"?S=m:(S={type:"element",tagName:"p",properties:{},children:[]},u.unshift(S)),S.children.length>0&&S.children.unshift({type:"text",value:" "}),S.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:a.checked,disabled:!0},children:[]}),f.className=["task-list-item"]}let h=-1;for(;++h<u.length;){const m=u[h];(s||h!==0||m.type!=="element"||m.tagName!=="p")&&d.push({type:"text",value:`
`}),m.type==="element"&&m.tagName==="p"&&!s?d.push(...m.children):d.push(m)}const b=u[u.length-1];b&&(s||b.type!=="element"||b.tagName!=="p")&&d.push({type:"text",value:`
`});const p={type:"element",tagName:"li",properties:f,children:d};return l.patch(a,p),l.applyData(a,p)}function Jv(l){let a=!1;if(l.type==="list"){a=l.spread||!1;const r=l.children;let u=-1;for(;!a&&++u<r.length;)a=Ny(r[u])}return a}function Ny(l){const a=l.spread;return a??l.children.length>1}function Fv(l,a){const r={},u=l.all(a);let s=-1;for(typeof a.start=="number"&&a.start!==1&&(r.start=a.start);++s<u.length;){const d=u[s];if(d.type==="element"&&d.tagName==="li"&&d.properties&&Array.isArray(d.properties.className)&&d.properties.className.includes("task-list-item")){r.className=["contains-task-list"];break}}const f={type:"element",tagName:a.ordered?"ol":"ul",properties:r,children:l.wrap(u,!0)};return l.patch(a,f),l.applyData(a,f)}function Pv(l,a){const r={type:"element",tagName:"p",properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}function Iv(l,a){const r={type:"root",children:l.wrap(l.all(a))};return l.patch(a,r),l.applyData(a,r)}function $v(l,a){const r={type:"element",tagName:"strong",properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}function Wv(l,a){const r=l.all(a),u=r.shift(),s=[];if(u){const d={type:"element",tagName:"thead",properties:{},children:l.wrap([u],!0)};l.patch(a.children[0],d),s.push(d)}if(r.length>0){const d={type:"element",tagName:"tbody",properties:{},children:l.wrap(r,!0)},h=Sc(a.children[1]),b=sy(a.children[a.children.length-1]);h&&b&&(d.position={start:h,end:b}),s.push(d)}const f={type:"element",tagName:"table",properties:{},children:l.wrap(s,!0)};return l.patch(a,f),l.applyData(a,f)}function eE(l,a,r){const u=r?r.children:void 0,f=(u?u.indexOf(a):1)===0?"th":"td",d=r&&r.type==="table"?r.align:void 0,h=d?d.length:a.children.length;let b=-1;const p=[];for(;++b<h;){const S=a.children[b],T={},v=d?d[b]:void 0;v&&(T.align=v);let _={type:"element",tagName:f,properties:T,children:[]};S&&(_.children=l.all(S),l.patch(S,_),_=l.applyData(S,_)),p.push(_)}const m={type:"element",tagName:"tr",properties:{},children:l.wrap(p,!0)};return l.patch(a,m),l.applyData(a,m)}function tE(l,a){const r={type:"element",tagName:"td",properties:{},children:l.all(a)};return l.patch(a,r),l.applyData(a,r)}const pm=9,mm=32;function nE(l){const a=String(l),r=/\r?\n|\r/g;let u=r.exec(a),s=0;const f=[];for(;u;)f.push(ym(a.slice(s,u.index),s>0,!0),u[0]),s=u.index+u[0].length,u=r.exec(a);return f.push(ym(a.slice(s),s>0,!1)),f.join("")}function ym(l,a,r){let u=0,s=l.length;if(a){let f=l.codePointAt(u);for(;f===pm||f===mm;)u++,f=l.codePointAt(u)}if(r){let f=l.codePointAt(s-1);for(;f===pm||f===mm;)s--,f=l.codePointAt(s-1)}return s>u?l.slice(u,s):""}function lE(l,a){const r={type:"text",value:nE(String(a.value))};return l.patch(a,r),l.applyData(a,r)}function iE(l,a){const r={type:"element",tagName:"hr",properties:{},children:[]};return l.patch(a,r),l.applyData(a,r)}const aE={blockquote:Mv,break:jv,code:Uv,delete:Bv,emphasis:Lv,footnoteReference:Hv,heading:qv,html:Yv,imageReference:Vv,image:Xv,inlineCode:Gv,linkReference:Qv,link:Zv,listItem:Kv,list:Fv,paragraph:Pv,root:Iv,strong:$v,table:Wv,tableCell:tE,tableRow:eE,text:lE,thematicBreak:iE,toml:Jr,yaml:Jr,definition:Jr,footnoteDefinition:Jr};function Jr(){}const Ry=-1,du=0,Oa=1,lu=2,_c=3,Cc=4,Nc=5,Rc=6,zy=7,Dy=8,gm=typeof self=="object"?self:globalThis,rE=(l,a)=>{const r=(s,f)=>(l.set(f,s),s),u=s=>{if(l.has(s))return l.get(s);const[f,d]=a[s];switch(f){case du:case Ry:return r(d,s);case Oa:{const h=r([],s);for(const b of d)h.push(u(b));return h}case lu:{const h=r({},s);for(const[b,p]of d)h[u(b)]=u(p);return h}case _c:return r(new Date(d),s);case Cc:{const{source:h,flags:b}=d;return r(new RegExp(h,b),s)}case Nc:{const h=r(new Map,s);for(const[b,p]of d)h.set(u(b),u(p));return h}case Rc:{const h=r(new Set,s);for(const b of d)h.add(u(b));return h}case zy:{const{name:h,message:b}=d;return r(new gm[h](b),s)}case Dy:return r(BigInt(d),s);case"BigInt":return r(Object(BigInt(d)),s);case"ArrayBuffer":return r(new Uint8Array(d).buffer,d);case"DataView":{const{buffer:h}=new Uint8Array(d);return r(new DataView(h),d)}}return r(new gm[f](d),s)};return u},bm=l=>rE(new Map,l)(0),ci="",{toString:uE}={},{keys:oE}=Object,Ta=l=>{const a=typeof l;if(a!=="object"||!l)return[du,a];const r=uE.call(l).slice(8,-1);switch(r){case"Array":return[Oa,ci];case"Object":return[lu,ci];case"Date":return[_c,ci];case"RegExp":return[Cc,ci];case"Map":return[Nc,ci];case"Set":return[Rc,ci];case"DataView":return[Oa,r]}return r.includes("Array")?[Oa,r]:r.includes("Error")?[zy,r]:[lu,r]},Fr=([l,a])=>l===du&&(a==="function"||a==="symbol"),sE=(l,a,r,u)=>{const s=(d,h)=>{const b=u.push(d)-1;return r.set(h,b),b},f=d=>{if(r.has(d))return r.get(d);let[h,b]=Ta(d);switch(h){case du:{let m=d;switch(b){case"bigint":h=Dy,m=d.toString();break;case"function":case"symbol":if(l)throw new TypeError("unable to serialize "+b);m=null;break;case"undefined":return s([Ry],d)}return s([h,m],d)}case Oa:{if(b){let T=d;return b==="DataView"?T=new Uint8Array(d.buffer):b==="ArrayBuffer"&&(T=new Uint8Array(d)),s([b,[...T]],d)}const m=[],S=s([h,m],d);for(const T of d)m.push(f(T));return S}case lu:{if(b)switch(b){case"BigInt":return s([b,d.toString()],d);case"Boolean":case"Number":case"String":return s([b,d.valueOf()],d)}if(a&&"toJSON"in d)return f(d.toJSON());const m=[],S=s([h,m],d);for(const T of oE(d))(l||!Fr(Ta(d[T])))&&m.push([f(T),f(d[T])]);return S}case _c:return s([h,d.toISOString()],d);case Cc:{const{source:m,flags:S}=d;return s([h,{source:m,flags:S}],d)}case Nc:{const m=[],S=s([h,m],d);for(const[T,v]of d)(l||!(Fr(Ta(T))||Fr(Ta(v))))&&m.push([f(T),f(v)]);return S}case Rc:{const m=[],S=s([h,m],d);for(const T of d)(l||!Fr(Ta(T)))&&m.push(f(T));return S}}const{message:p}=d;return s([h,{name:b,message:p}],d)};return f},xm=(l,{json:a,lossy:r}={})=>{const u=[];return sE(!(a||r),!!a,new Map,u)(l),u},iu=typeof structuredClone=="function"?(l,a)=>a&&("json"in a||"lossy"in a)?bm(xm(l,a)):structuredClone(l):(l,a)=>bm(xm(l,a));function cE(l,a){const r=[{type:"text",value:"↩"}];return a>1&&r.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(a)}]}),r}function fE(l,a){return"Back to reference "+(l+1)+(a>1?"-"+a:"")}function dE(l){const a=typeof l.options.clobberPrefix=="string"?l.options.clobberPrefix:"user-content-",r=l.options.footnoteBackContent||cE,u=l.options.footnoteBackLabel||fE,s=l.options.footnoteLabel||"Footnotes",f=l.options.footnoteLabelTagName||"h2",d=l.options.footnoteLabelProperties||{className:["sr-only"]},h=[];let b=-1;for(;++b<l.footnoteOrder.length;){const p=l.footnoteById.get(l.footnoteOrder[b]);if(!p)continue;const m=l.all(p),S=String(p.identifier).toUpperCase(),T=yi(S.toLowerCase());let v=0;const _=[],U=l.footnoteCounts.get(S);for(;U!==void 0&&++v<=U;){_.length>0&&_.push({type:"text",value:" "});let j=typeof r=="string"?r:r(b,v);typeof j=="string"&&(j={type:"text",value:j}),_.push({type:"element",tagName:"a",properties:{href:"#"+a+"fnref-"+T+(v>1?"-"+v:""),dataFootnoteBackref:"",ariaLabel:typeof u=="string"?u:u(b,v),className:["data-footnote-backref"]},children:Array.isArray(j)?j:[j]})}const C=m[m.length-1];if(C&&C.type==="element"&&C.tagName==="p"){const j=C.children[C.children.length-1];j&&j.type==="text"?j.value+=" ":C.children.push({type:"text",value:" "}),C.children.push(..._)}else m.push(..._);const N={type:"element",tagName:"li",properties:{id:a+"fn-"+T},children:l.wrap(m,!0)};l.patch(p,N),h.push(N)}if(h.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:f,properties:{...iu(d),id:"footnote-label"},children:[{type:"text",value:s}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:l.wrap(h,!0)},{type:"text",value:`
`}]}}const ky=(function(l){if(l==null)return yE;if(typeof l=="function")return hu(l);if(typeof l=="object")return Array.isArray(l)?hE(l):pE(l);if(typeof l=="string")return mE(l);throw new Error("Expected function, string, or object as test")});function hE(l){const a=[];let r=-1;for(;++r<l.length;)a[r]=ky(l[r]);return hu(u);function u(...s){let f=-1;for(;++f<a.length;)if(a[f].apply(this,s))return!0;return!1}}function pE(l){const a=l;return hu(r);function r(u){const s=u;let f;for(f in l)if(s[f]!==a[f])return!1;return!0}}function mE(l){return hu(a);function a(r){return r&&r.type===l}}function hu(l){return a;function a(r,u,s){return!!(gE(r)&&l.call(this,r,typeof u=="number"?u:void 0,s||void 0))}}function yE(){return!0}function gE(l){return l!==null&&typeof l=="object"&&"type"in l}const My=[],bE=!0,Sm=!1,xE="skip";function SE(l,a,r,u){let s;typeof a=="function"&&typeof r!="function"?(u=r,r=a):s=a;const f=ky(s),d=u?-1:1;h(l,void 0,[])();function h(b,p,m){const S=b&&typeof b=="object"?b:{};if(typeof S.type=="string"){const v=typeof S.tagName=="string"?S.tagName:typeof S.name=="string"?S.name:void 0;Object.defineProperty(T,"name",{value:"node ("+(b.type+(v?"<"+v+">":""))+")"})}return T;function T(){let v=My,_,U,C;if((!a||f(b,p,m[m.length-1]||void 0))&&(v=vE(r(b,m)),v[0]===Sm))return v;if("children"in b&&b.children){const N=b;if(N.children&&v[0]!==xE)for(U=(u?N.children.length:-1)+d,C=m.concat(N);U>-1&&U<N.children.length;){const j=N.children[U];if(_=h(j,U,C)(),_[0]===Sm)return _;U=typeof _[1]=="number"?_[1]:U+d}}return v}}}function vE(l){return Array.isArray(l)?l:typeof l=="number"?[bE,l]:l==null?My:[l]}function jy(l,a,r,u){let s,f,d;typeof a=="function"&&typeof r!="function"?(f=void 0,d=a,s=r):(f=a,d=r,s=u),SE(l,f,h,s);function h(b,p){const m=p[p.length-1],S=m?m.children.indexOf(b):void 0;return d(b,S,m)}}const fc={}.hasOwnProperty,EE={};function TE(l,a){const r=a||EE,u=new Map,s=new Map,f=new Map,d={...aE,...r.handlers},h={all:p,applyData:wE,definitionById:u,footnoteById:s,footnoteCounts:f,footnoteOrder:[],handlers:d,one:b,options:r,patch:AE,wrap:_E};return jy(l,function(m){if(m.type==="definition"||m.type==="footnoteDefinition"){const S=m.type==="definition"?u:s,T=String(m.identifier).toUpperCase();S.has(T)||S.set(T,m)}}),h;function b(m,S){const T=m.type,v=h.handlers[T];if(fc.call(h.handlers,T)&&v)return v(h,m,S);if(h.options.passThrough&&h.options.passThrough.includes(T)){if("children"in m){const{children:U,...C}=m,N=iu(C);return N.children=h.all(m),N}return iu(m)}return(h.options.unknownHandler||OE)(h,m,S)}function p(m){const S=[];if("children"in m){const T=m.children;let v=-1;for(;++v<T.length;){const _=h.one(T[v],m);if(_){if(v&&T[v-1].type==="break"&&(!Array.isArray(_)&&_.type==="text"&&(_.value=vm(_.value)),!Array.isArray(_)&&_.type==="element")){const U=_.children[0];U&&U.type==="text"&&(U.value=vm(U.value))}Array.isArray(_)?S.push(..._):S.push(_)}}}return S}}function AE(l,a){l.position&&(a.position=ux(l))}function wE(l,a){let r=a;if(l&&l.data){const u=l.data.hName,s=l.data.hChildren,f=l.data.hProperties;if(typeof u=="string")if(r.type==="element")r.tagName=u;else{const d="children"in r?r.children:[r];r={type:"element",tagName:u,properties:{},children:d}}r.type==="element"&&f&&Object.assign(r.properties,iu(f)),"children"in r&&r.children&&s!==null&&s!==void 0&&(r.children=s)}return r}function OE(l,a){const r=a.data||{},u="value"in a&&!(fc.call(r,"hProperties")||fc.call(r,"hChildren"))?{type:"text",value:a.value}:{type:"element",tagName:"div",properties:{},children:l.all(a)};return l.patch(a,u),l.applyData(a,u)}function _E(l,a){const r=[];let u=-1;for(a&&r.push({type:"text",value:`
`});++u<l.length;)u&&r.push({type:"text",value:`
`}),r.push(l[u]);return a&&l.length>0&&r.push({type:"text",value:`
`}),r}function vm(l){let a=0,r=l.charCodeAt(a);for(;r===9||r===32;)a++,r=l.charCodeAt(a);return l.slice(a)}function Em(l,a){const r=TE(l,a),u=r.one(l,void 0),s=dE(r),f=Array.isArray(u)?{type:"root",children:u}:u||{type:"root",children:[]};return s&&f.children.push({type:"text",value:`
`},s),f}function CE(l,a){return l&&"run"in l?async function(r,u){const s=Em(r,{file:u,...a});await l.run(s,u)}:function(r,u){return Em(r,{file:u,...l||a})}}function Tm(l){if(l)throw l}var Gs,Am;function NE(){if(Am)return Gs;Am=1;var l=Object.prototype.hasOwnProperty,a=Object.prototype.toString,r=Object.defineProperty,u=Object.getOwnPropertyDescriptor,s=function(p){return typeof Array.isArray=="function"?Array.isArray(p):a.call(p)==="[object Array]"},f=function(p){if(!p||a.call(p)!=="[object Object]")return!1;var m=l.call(p,"constructor"),S=p.constructor&&p.constructor.prototype&&l.call(p.constructor.prototype,"isPrototypeOf");if(p.constructor&&!m&&!S)return!1;var T;for(T in p);return typeof T>"u"||l.call(p,T)},d=function(p,m){r&&m.name==="__proto__"?r(p,m.name,{enumerable:!0,configurable:!0,value:m.newValue,writable:!0}):p[m.name]=m.newValue},h=function(p,m){if(m==="__proto__")if(l.call(p,m)){if(u)return u(p,m).value}else return;return p[m]};return Gs=function b(){var p,m,S,T,v,_,U=arguments[0],C=1,N=arguments.length,j=!1;for(typeof U=="boolean"&&(j=U,U=arguments[1]||{},C=2),(U==null||typeof U!="object"&&typeof U!="function")&&(U={});C<N;++C)if(p=arguments[C],p!=null)for(m in p)S=h(U,m),T=h(p,m),U!==T&&(j&&T&&(f(T)||(v=s(T)))?(v?(v=!1,_=S&&s(S)?S:[]):_=S&&f(S)?S:{},d(U,{name:m,newValue:b(j,_,T)})):typeof T<"u"&&d(U,{name:m,newValue:T}));return U},Gs}var RE=NE();const Qs=Rm(RE);function dc(l){if(typeof l!="object"||l===null)return!1;const a=Object.getPrototypeOf(l);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Symbol.toStringTag in l)&&!(Symbol.iterator in l)}function zE(){const l=[],a={run:r,use:u};return a;function r(...s){let f=-1;const d=s.pop();if(typeof d!="function")throw new TypeError("Expected function as last argument, not "+d);h(null,...s);function h(b,...p){const m=l[++f];let S=-1;if(b){d(b);return}for(;++S<s.length;)(p[S]===null||p[S]===void 0)&&(p[S]=s[S]);s=p,m?DE(m,h)(...p):d(null,...p)}}function u(s){if(typeof s!="function")throw new TypeError("Expected `middelware` to be a function, not "+s);return l.push(s),a}}function DE(l,a){let r;return u;function u(...d){const h=l.length>d.length;let b;h&&d.push(s);try{b=l.apply(this,d)}catch(p){const m=p;if(h&&r)throw m;return s(m)}h||(b&&b.then&&typeof b.then=="function"?b.then(f,s):b instanceof Error?s(b):f(b))}function s(d,...h){r||(r=!0,a(d,...h))}function f(d){s(null,d)}}const fn={basename:kE,dirname:ME,extname:jE,join:UE,sep:"/"};function kE(l,a){if(a!==void 0&&typeof a!="string")throw new TypeError('"ext" argument must be a string');Ma(l);let r=0,u=-1,s=l.length,f;if(a===void 0||a.length===0||a.length>l.length){for(;s--;)if(l.codePointAt(s)===47){if(f){r=s+1;break}}else u<0&&(f=!0,u=s+1);return u<0?"":l.slice(r,u)}if(a===l)return"";let d=-1,h=a.length-1;for(;s--;)if(l.codePointAt(s)===47){if(f){r=s+1;break}}else d<0&&(f=!0,d=s+1),h>-1&&(l.codePointAt(s)===a.codePointAt(h--)?h<0&&(u=s):(h=-1,u=d));return r===u?u=d:u<0&&(u=l.length),l.slice(r,u)}function ME(l){if(Ma(l),l.length===0)return".";let a=-1,r=l.length,u;for(;--r;)if(l.codePointAt(r)===47){if(u){a=r;break}}else u||(u=!0);return a<0?l.codePointAt(0)===47?"/":".":a===1&&l.codePointAt(0)===47?"//":l.slice(0,a)}function jE(l){Ma(l);let a=l.length,r=-1,u=0,s=-1,f=0,d;for(;a--;){const h=l.codePointAt(a);if(h===47){if(d){u=a+1;break}continue}r<0&&(d=!0,r=a+1),h===46?s<0?s=a:f!==1&&(f=1):s>-1&&(f=-1)}return s<0||r<0||f===0||f===1&&s===r-1&&s===u+1?"":l.slice(s,r)}function UE(...l){let a=-1,r;for(;++a<l.length;)Ma(l[a]),l[a]&&(r=r===void 0?l[a]:r+"/"+l[a]);return r===void 0?".":BE(r)}function BE(l){Ma(l);const a=l.codePointAt(0)===47;let r=LE(l,!a);return r.length===0&&!a&&(r="."),r.length>0&&l.codePointAt(l.length-1)===47&&(r+="/"),a?"/"+r:r}function LE(l,a){let r="",u=0,s=-1,f=0,d=-1,h,b;for(;++d<=l.length;){if(d<l.length)h=l.codePointAt(d);else{if(h===47)break;h=47}if(h===47){if(!(s===d-1||f===1))if(s!==d-1&&f===2){if(r.length<2||u!==2||r.codePointAt(r.length-1)!==46||r.codePointAt(r.length-2)!==46){if(r.length>2){if(b=r.lastIndexOf("/"),b!==r.length-1){b<0?(r="",u=0):(r=r.slice(0,b),u=r.length-1-r.lastIndexOf("/")),s=d,f=0;continue}}else if(r.length>0){r="",u=0,s=d,f=0;continue}}a&&(r=r.length>0?r+"/..":"..",u=2)}else r.length>0?r+="/"+l.slice(s+1,d):r=l.slice(s+1,d),u=d-s-1;s=d,f=0}else h===46&&f>-1?f++:f=-1}return r}function Ma(l){if(typeof l!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(l))}const HE={cwd:qE};function qE(){return"/"}function hc(l){return!!(l!==null&&typeof l=="object"&&"href"in l&&l.href&&"protocol"in l&&l.protocol&&l.auth===void 0)}function YE(l){if(typeof l=="string")l=new URL(l);else if(!hc(l)){const a=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+l+"`");throw a.code="ERR_INVALID_ARG_TYPE",a}if(l.protocol!=="file:"){const a=new TypeError("The URL must be of scheme file");throw a.code="ERR_INVALID_URL_SCHEME",a}return VE(l)}function VE(l){if(l.hostname!==""){const u=new TypeError('File URL host must be "localhost" or empty on darwin');throw u.code="ERR_INVALID_FILE_URL_HOST",u}const a=l.pathname;let r=-1;for(;++r<a.length;)if(a.codePointAt(r)===37&&a.codePointAt(r+1)===50){const u=a.codePointAt(r+2);if(u===70||u===102){const s=new TypeError("File URL path must not include encoded / characters");throw s.code="ERR_INVALID_FILE_URL_PATH",s}}return decodeURIComponent(a)}const Zs=["history","path","basename","stem","extname","dirname"];class Uy{constructor(a){let r;a?hc(a)?r={path:a}:typeof a=="string"||XE(a)?r={value:a}:r=a:r={},this.cwd="cwd"in r?"":HE.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let u=-1;for(;++u<Zs.length;){const f=Zs[u];f in r&&r[f]!==void 0&&r[f]!==null&&(this[f]=f==="history"?[...r[f]]:r[f])}let s;for(s in r)Zs.includes(s)||(this[s]=r[s])}get basename(){return typeof this.path=="string"?fn.basename(this.path):void 0}set basename(a){Js(a,"basename"),Ks(a,"basename"),this.path=fn.join(this.dirname||"",a)}get dirname(){return typeof this.path=="string"?fn.dirname(this.path):void 0}set dirname(a){wm(this.basename,"dirname"),this.path=fn.join(a||"",this.basename)}get extname(){return typeof this.path=="string"?fn.extname(this.path):void 0}set extname(a){if(Ks(a,"extname"),wm(this.dirname,"extname"),a){if(a.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(a.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=fn.join(this.dirname,this.stem+(a||""))}get path(){return this.history[this.history.length-1]}set path(a){hc(a)&&(a=YE(a)),Js(a,"path"),this.path!==a&&this.history.push(a)}get stem(){return typeof this.path=="string"?fn.basename(this.path,this.extname):void 0}set stem(a){Js(a,"stem"),Ks(a,"stem"),this.path=fn.join(this.dirname||"",a+(this.extname||""))}fail(a,r,u){const s=this.message(a,r,u);throw s.fatal=!0,s}info(a,r,u){const s=this.message(a,r,u);return s.fatal=void 0,s}message(a,r,u){const s=new pt(a,r,u);return this.path&&(s.name=this.path+":"+s.name,s.file=this.path),s.fatal=!1,this.messages.push(s),s}toString(a){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(a||void 0).decode(this.value)}}function Ks(l,a){if(l&&l.includes(fn.sep))throw new Error("`"+a+"` cannot be a path: did not expect `"+fn.sep+"`")}function Js(l,a){if(!l)throw new Error("`"+a+"` cannot be empty")}function wm(l,a){if(!l)throw new Error("Setting `"+a+"` requires `path` to be set too")}function XE(l){return!!(l&&typeof l=="object"&&"byteLength"in l&&"byteOffset"in l)}const GE=(function(l){const u=this.constructor.prototype,s=u[l],f=function(){return s.apply(f,arguments)};return Object.setPrototypeOf(f,u),f}),QE={}.hasOwnProperty;class zc extends GE{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=zE()}copy(){const a=new zc;let r=-1;for(;++r<this.attachers.length;){const u=this.attachers[r];a.use(...u)}return a.data(Qs(!0,{},this.namespace)),a}data(a,r){return typeof a=="string"?arguments.length===2?(Is("data",this.frozen),this.namespace[a]=r,this):QE.call(this.namespace,a)&&this.namespace[a]||void 0:a?(Is("data",this.frozen),this.namespace=a,this):this.namespace}freeze(){if(this.frozen)return this;const a=this;for(;++this.freezeIndex<this.attachers.length;){const[r,...u]=this.attachers[this.freezeIndex];if(u[0]===!1)continue;u[0]===!0&&(u[0]=void 0);const s=r.call(a,...u);typeof s=="function"&&this.transformers.use(s)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(a){this.freeze();const r=Pr(a),u=this.parser||this.Parser;return Fs("parse",u),u(String(r),r)}process(a,r){const u=this;return this.freeze(),Fs("process",this.parser||this.Parser),Ps("process",this.compiler||this.Compiler),r?s(void 0,r):new Promise(s);function s(f,d){const h=Pr(a),b=u.parse(h);u.run(b,h,function(m,S,T){if(m||!S||!T)return p(m);const v=S,_=u.stringify(v,T);JE(_)?T.value=_:T.result=_,p(m,T)});function p(m,S){m||!S?d(m):f?f(S):r(void 0,S)}}}processSync(a){let r=!1,u;return this.freeze(),Fs("processSync",this.parser||this.Parser),Ps("processSync",this.compiler||this.Compiler),this.process(a,s),_m("processSync","process",r),u;function s(f,d){r=!0,Tm(f),u=d}}run(a,r,u){Om(a),this.freeze();const s=this.transformers;return!u&&typeof r=="function"&&(u=r,r=void 0),u?f(void 0,u):new Promise(f);function f(d,h){const b=Pr(r);s.run(a,b,p);function p(m,S,T){const v=S||a;m?h(m):d?d(v):u(void 0,v,T)}}}runSync(a,r){let u=!1,s;return this.run(a,r,f),_m("runSync","run",u),s;function f(d,h){Tm(d),s=h,u=!0}}stringify(a,r){this.freeze();const u=Pr(r),s=this.compiler||this.Compiler;return Ps("stringify",s),Om(a),s(a,u)}use(a,...r){const u=this.attachers,s=this.namespace;if(Is("use",this.frozen),a!=null)if(typeof a=="function")b(a,r);else if(typeof a=="object")Array.isArray(a)?h(a):d(a);else throw new TypeError("Expected usable value, not `"+a+"`");return this;function f(p){if(typeof p=="function")b(p,[]);else if(typeof p=="object")if(Array.isArray(p)){const[m,...S]=p;b(m,S)}else d(p);else throw new TypeError("Expected usable value, not `"+p+"`")}function d(p){if(!("plugins"in p)&&!("settings"in p))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");h(p.plugins),p.settings&&(s.settings=Qs(!0,s.settings,p.settings))}function h(p){let m=-1;if(p!=null)if(Array.isArray(p))for(;++m<p.length;){const S=p[m];f(S)}else throw new TypeError("Expected a list of plugins, not `"+p+"`")}function b(p,m){let S=-1,T=-1;for(;++S<u.length;)if(u[S][0]===p){T=S;break}if(T===-1)u.push([p,...m]);else if(m.length>0){let[v,..._]=m;const U=u[T][1];dc(U)&&dc(v)&&(v=Qs(!0,U,v)),u[T]=[p,v,..._]}}}}const ZE=new zc().freeze();function Fs(l,a){if(typeof a!="function")throw new TypeError("Cannot `"+l+"` without `parser`")}function Ps(l,a){if(typeof a!="function")throw new TypeError("Cannot `"+l+"` without `compiler`")}function Is(l,a){if(a)throw new Error("Cannot call `"+l+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Om(l){if(!dc(l)||typeof l.type!="string")throw new TypeError("Expected node, got `"+l+"`")}function _m(l,a,r){if(!r)throw new Error("`"+l+"` finished async. Use `"+a+"` instead")}function Pr(l){return KE(l)?l:new Uy(l)}function KE(l){return!!(l&&typeof l=="object"&&"message"in l&&"messages"in l)}function JE(l){return typeof l=="string"||FE(l)}function FE(l){return!!(l&&typeof l=="object"&&"byteLength"in l&&"byteOffset"in l)}const PE="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Cm=[],Nm={allowDangerousHtml:!0},IE=/^(https?|ircs?|mailto|xmpp)$/i,$E=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function WE(l){const a=e2(l),r=t2(l);return n2(a.runSync(a.parse(r),r),l)}function e2(l){const a=l.rehypePlugins||Cm,r=l.remarkPlugins||Cm,u=l.remarkRehypeOptions?{...l.remarkRehypeOptions,...Nm}:Nm;return ZE().use(kv).use(r).use(CE,u).use(a)}function t2(l){const a=l.children||"",r=new Uy;return typeof a=="string"&&(r.value=a),r}function n2(l,a){const r=a.allowedElements,u=a.allowElement,s=a.components,f=a.disallowedElements,d=a.skipHtml,h=a.unwrapDisallowed,b=a.urlTransform||l2;for(const m of $E)Object.hasOwn(a,m.from)&&(""+m.from+(m.to?"use `"+m.to+"` instead":"remove it")+PE+m.id,void 0);return jy(l,p),dx(l,{Fragment:z.Fragment,components:s,ignoreInvalidStyle:!0,jsx:z.jsx,jsxs:z.jsxs,passKeys:!0,passNode:!0});function p(m,S,T){if(m.type==="raw"&&T&&typeof S=="number")return d?T.children.splice(S,1):T.children[S]={type:"text",value:m.value},S;if(m.type==="element"){let v;for(v in Ys)if(Object.hasOwn(Ys,v)&&Object.hasOwn(m.properties,v)){const _=m.properties[v],U=Ys[v];(U===null||U.includes(m.tagName))&&(m.properties[v]=b(String(_||""),v,m))}}if(m.type==="element"){let v=r?!r.includes(m.tagName):f?f.includes(m.tagName):!1;if(!v&&u&&typeof S=="number"&&(v=!u(m,S,T)),v&&T&&typeof S=="number")return h&&m.children?T.children.splice(S,1,...m.children):T.children.splice(S,1),S}}}function l2(l){const a=l.indexOf(":"),r=l.indexOf("?"),u=l.indexOf("#"),s=l.indexOf("/");return a===-1||s!==-1&&a>s||r!==-1&&a>r||u!==-1&&a>u||IE.test(l.slice(0,a))?l:""}function i2({content:l,isLoading:a,selectedTopic:r}){return a?z.jsxs("div",{className:"flex flex-col items-center justify-center h-full",children:[z.jsx("div",{className:"w-12 h-12 border-4 border-gray-300 border-t-blue-400 rounded-full animate-spin"}),z.jsx("p",{className:"mt-5 text-gray-400",children:"Generating your course..."})]}):r?l?z.jsx("div",{className:"text-gray-300 leading-relaxed [&_h1]:text-blue-400 [&_h1]:mt-6 [&_h1]:mb-4 [&_h2]:text-blue-400 [&_h2]:mt-6 [&_h2]:mb-4 [&_h3]:text-blue-400 [&_h3]:mt-6 [&_h3]:mb-4 [&_h4]:text-blue-400 [&_h4]:mt-6 [&_h4]:mb-4 [&_h5]:text-blue-400 [&_h5]:mt-6 [&_h5]:mb-4 [&_h6]:text-blue-400 [&_h6]:mt-6 [&_h6]:mb-4 [&_p]:mb-4 [&_pre]:bg-gray-800 [&_pre]:p-4 [&_pre]:rounded-lg [&_pre]:overflow-x-auto [&_pre]:mb-4 [&_code]:bg-blue-400/10 [&_code]:px-1 [&_code]:py-0.5 [&_code]:rounded [&_code]:font-mono [&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_ul]:mb-4 [&_ul]:pl-6 [&_ol]:mb-4 [&_ol]:pl-6 [&_li]:mb-2 [&_blockquote]:border-l-4 [&_blockquote]:border-blue-400 [&_blockquote]:pl-4 [&_blockquote]:my-4 [&_blockquote]:text-gray-400",children:z.jsx(WE,{children:l})}):z.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center text-gray-400",children:[z.jsx("h2",{className:"mb-4 text-blue-400",children:r.title}),z.jsx("p",{children:'Click "Generate New Course" to create a personalized learning module.'})]}):z.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center text-gray-400",children:[z.jsx("h2",{className:"mb-4 text-blue-400",children:"Welcome to QtMaster.io"}),z.jsx("p",{children:"Select a topic from the curriculum to get started."})]})}function a2({subject:l,onBack:a,apiKey:r}){const[u,s]=Ve.useState(null),[f,d]=Ve.useState(null),[h,b]=Ve.useState(""),[p,m]=Ve.useState(!1),[S,T]=Ve.useState(!0),[v,_]=Ve.useState(null);Ve.useEffect(()=>{U()},[l.id]);const U=async()=>{try{T(!0);const j=await He.get(`http://localhost:3001/api/curricula/${l.id}`);s(j.data.structure.curriculum),_(null)}catch(j){console.error("Error fetching curriculum:",j),_("Failed to load curriculum. Please try again.")}finally{T(!1)}},C=j=>{d(j),b("")},N=async()=>{if(!f){alert("Please select a topic first");return}if(!r){alert("Please set your API key in the settings");return}m(!0);try{const j=await He.post("http://localhost:3001/api/generate",{topic:f.title,subjectId:l.id,topicId:f.id,apiKey:r});b(j.data.content)}catch(j){console.error("Error generating course:",j),j.response?alert(`Error: ${j.response.data.error||"Unknown error"}`):j.request?alert("Error: No response from server. Make sure the backend is running."):alert(`Error: ${j.message}`)}finally{m(!1)}};return S?z.jsxs("div",{className:"flex flex-col items-center justify-center h-screen",children:[z.jsx("div",{className:"w-12 h-12 border-4 border-gray-300 border-t-blue-400 rounded-full animate-spin"}),z.jsx("p",{className:"mt-5 text-gray-400",children:"Loading curriculum..."})]}):v?z.jsxs("div",{className:"flex flex-col items-center justify-center h-screen text-center text-gray-400 p-5",children:[z.jsx("h2",{className:"text-red-400 mb-2",children:"Error"}),z.jsx("p",{children:v}),z.jsxs("div",{className:"flex gap-4 mt-5",children:[z.jsx("button",{onClick:U,className:"bg-blue-600 text-white border-none px-5 py-2 rounded cursor-pointer font-bold hover:bg-blue-700 transition-colors duration-200",children:"Try Again"}),z.jsx("button",{onClick:a,className:"bg-transparent border border-blue-400 text-blue-400 px-4 py-2 rounded cursor-pointer transition-all duration-200 whitespace-nowrap hover:bg-blue-400 hover:text-white",children:"Back to Dashboard"})]})]}):z.jsxs("div",{className:"h-screen flex flex-col",children:[z.jsxs("div",{className:"flex items-center gap-5 p-5 border-b border-gray-600 bg-gray-900",children:[z.jsx("button",{onClick:a,className:"bg-transparent border border-blue-400 text-blue-400 px-4 py-2 rounded cursor-pointer transition-all duration-200 whitespace-nowrap hover:bg-blue-400 hover:text-white",children:"← Back to Dashboard"}),z.jsxs("div",{className:"flex-1",children:[z.jsx("h1",{className:"text-blue-400 m-0 mb-1 text-2xl",children:l.title}),l.description&&z.jsx("p",{className:"text-gray-400 m-0 text-sm",children:l.description})]})]}),z.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[z.jsx("div",{className:"w-80 bg-gray-900 border-r border-gray-600 overflow-y-auto",children:z.jsx(B1,{curriculum:u,onTopicSelect:C,selectedTopic:f,subjectTitle:l.title})}),z.jsxs("div",{className:"flex-1 flex flex-col bg-gray-800",children:[f&&z.jsx("div",{className:"p-5 border-b border-gray-600 bg-gray-900",children:z.jsx("button",{className:`bg-blue-600 text-white border-none px-6 py-3 rounded cursor-pointer font-bold transition-colors duration-200 ${p?"bg-gray-600 cursor-not-allowed opacity-60":"hover:bg-blue-700"}`,onClick:N,disabled:p,children:p?"Generating...":"Generate New Course"})}),z.jsx(i2,{content:h,isLoading:p,selectedTopic:f})]})]})]})}function r2({onSettingsClick:l}){return z.jsxs("header",{className:"flex justify-between items-center px-5 py-4 bg-gray-800 border-b border-gray-600",children:[z.jsx("h1",{className:"text-2xl font-bold text-blue-400 m-0",children:"QtMaster.io"}),z.jsx("button",{className:"px-4 py-2 bg-gray-600 text-white border-none rounded cursor-pointer transition-colors duration-200 hover:bg-gray-500",onClick:l,children:"Settings"})]})}function u2({onClose:l,onSave:a,initialApiKey:r}){const[u,s]=Ve.useState(r||""),f=()=>{a(u)},d=h=>{h.target===h.currentTarget&&l()};return z.jsx("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center z-[1000]",onClick:d,children:z.jsxs("div",{className:"bg-gray-800 rounded-lg w-[90%] max-w-lg p-6 shadow-[0_4px_20px_rgba(0,0,0,0.5)]",children:[z.jsx("h2",{className:"mt-0 mb-5 text-blue-400",children:"Settings"}),z.jsxs("div",{className:"mb-5",children:[z.jsx("label",{htmlFor:"apiKey",className:"block mb-2 font-bold text-gray-300",children:"LLM API Key"}),z.jsx("input",{id:"apiKey",type:"password",value:u,onChange:h=>s(h.target.value),className:"w-full p-2 bg-gray-700 border border-gray-600 rounded text-gray-300 text-sm focus:outline-none focus:border-blue-400",placeholder:"Enter your API key"}),z.jsx("p",{className:"mt-2 text-xs text-gray-400",children:"Your API key is stored locally in your browser and is never sent to any server except the LLM API."})]}),z.jsxs("div",{className:"flex justify-end gap-2",children:[z.jsx("button",{onClick:l,className:"px-4 py-2 rounded border-none cursor-pointer font-bold bg-gray-600 text-gray-300 hover:bg-gray-500 transition-colors duration-200",children:"Cancel"}),z.jsx("button",{onClick:f,className:"px-4 py-2 rounded border-none cursor-pointer font-bold bg-blue-600 text-white hover:bg-blue-700 transition-colors duration-200",children:"Save"})]})]})})}function o2(){const[l,a]=Ve.useState("dashboard"),[r,u]=Ve.useState(null),[s,f]=Ve.useState(""),[d,h]=Ve.useState(!1);Ve.useEffect(()=>{const _=localStorage.getItem("qtmaster_api_key");_&&f(_)},[]);const b=_=>{u(_),a("learning")},p=()=>{a("create-subject")},m=_=>{u(_),a("learning")},S=()=>{u(null),a("dashboard")},T=_=>{f(_),localStorage.setItem("qtmaster_api_key",_),h(!1)},v=()=>{switch(l){case"dashboard":return z.jsx(Qp,{onSubjectSelect:b,onCreateSubject:p});case"create-subject":return z.jsx(U1,{onBack:S,onSubjectCreated:m,apiKey:s});case"learning":return z.jsx(a2,{subject:r,onBack:S,apiKey:s});default:return z.jsx(Qp,{onSubjectSelect:b,onCreateSubject:p})}};return z.jsxs("div",{className:"h-screen bg-gray-100 flex flex-col items-center justify-center",children:[l==="dashboard"&&z.jsx(r2,{onSettingsClick:()=>h(!0)}),v(),d&&z.jsx(u2,{onClose:()=>h(!1),onSave:T,initialApiKey:s})]})}ib.createRoot(document.getElementById("root")).render(z.jsx(Ve.StrictMode,{children:z.jsx(o2,{})}));
