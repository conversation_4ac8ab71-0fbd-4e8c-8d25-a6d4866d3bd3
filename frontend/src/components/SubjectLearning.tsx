import React, { useState, useEffect } from 'react';
import axios from 'axios';
import TopicTree from './TopicTree';
import ContentDisplay from './ContentDisplay';
import type { SubjectLearningProps, CurriculumCategory, Topic } from '../types';

const SubjectLearning: React.FC<SubjectLearningProps> = ({ subject, onBack, apiKey }) => {
  const [curriculum, setCurriculum] = useState<CurriculumCategory[] | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [courseContent, setCourseContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingCurriculum, setIsLoadingCurriculum] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCurriculum();
  }, [subject.id]);

  const fetchCurriculum = async () => {
    try {
      setIsLoadingCurriculum(true);
      const response = await axios.get(`http://localhost:3001/api/curricula/${subject.id}`);
      setCurriculum(response.data.structure.curriculum);
      setError(null);
    } catch (error) {
      console.error('Error fetching curriculum:', error);
      setError('Failed to load curriculum. Please try again.');
    } finally {
      setIsLoadingCurriculum(false);
    }
  };

  const handleTopicSelect = (topic: Topic) => {
    setSelectedTopic(topic);
    setCourseContent(''); // Clear previous content when selecting a new topic
  };

  const handleGenerateCourse = async () => {
    if (!selectedTopic) {
      alert('Please select a topic first');
      return;
    }
    
    if (!apiKey) {
      alert('Please set your API key in the settings');
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.post('http://localhost:3001/api/generate', {
        topic: selectedTopic.title,
        subjectId: subject.id,
        topicId: selectedTopic.id,
        apiKey: apiKey
      });
      
      setCourseContent(response.data.content);
    } catch (error: any) {
      console.error('Error generating course:', error);
      if (error.response) {
        alert(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        alert('Error: No response from server. Make sure the backend is running.');
      } else {
        alert(`Error: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingCurriculum) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mb-4 mx-auto"></div>
          <p className="text-gray-400">Loading curriculum...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-red-400 text-xl mb-2">Error</h2>
          <p className="text-gray-400 mb-4">{error}</p>
          <div className="flex gap-4 justify-center">
            <button onClick={fetchCurriculum} className="btn-primary">
              Try Again
            </button>
            <button onClick={onBack} className="btn-secondary">
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center gap-4">
          <button onClick={onBack} className="btn-secondary">
            ← Back to Dashboard
          </button>
          <div>
            <h1 className="text-2xl font-bold text-blue-400">{subject.title}</h1>
            {subject.description && <p className="text-gray-400 text-sm">{subject.description}</p>}
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className="w-80 bg-gray-800 border-r border-gray-700 overflow-y-auto">
          {curriculum && (
            <TopicTree
              curriculum={curriculum}
              onTopicSelect={handleTopicSelect}
              selectedTopic={selectedTopic}
              subjectTitle={subject.title}
            />
          )}
        </div>

        <div className="flex-1 flex flex-col bg-gray-900">
          {selectedTopic && (
            <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
              <button
                className="btn-primary"
                onClick={handleGenerateCourse}
                disabled={isLoading}
              >
                {isLoading ? 'Generating...' : 'Generate New Course'}
              </button>
            </div>
          )}
          <div className="flex-1 overflow-y-auto">
            <ContentDisplay
              content={courseContent}
              isLoading={isLoading}
              selectedTopic={selectedTopic}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubjectLearning;
