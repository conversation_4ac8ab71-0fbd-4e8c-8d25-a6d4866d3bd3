import React from 'react';
import type { HeaderProps } from '../types';

const Header: React.FC<HeaderProps> = ({ onSettingsClick }) => {
  return (
    <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">Q</span>
          </div>
          <h1 className="text-2xl font-bold text-blue-400">QtMaster.io</h1>
        </div>

        <button
          onClick={onSettingsClick}
          className="btn-secondary"
          title="Settings"
        >
          ⚙️ Settings
        </button>
      </div>
    </header>
  );
};

export default Header;
